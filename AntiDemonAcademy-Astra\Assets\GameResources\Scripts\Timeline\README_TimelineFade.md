# Timeline 黑幕淡入淡出功能

## 概述
在 `TimelineTrigger.cs` 中新增了黑幕淡入淡出功能，使用 `CameraManager.cs` 中的 storyboard 來調整 alpha 值，實現平滑的場景轉換效果。

## 功能特點
- **自動黑幕效果**: Timeline 開始前淡入，結束後淡出
- **可配置參數**: 支援自訂淡入/淡出時間和延遲
- **無縫整合**: 與現有的 Timeline 系統完全相容
- **效能優化**: 使用 `Time.unscaledDeltaTime` 確保不受時間縮放影響

## 使用方法

### 1. 基本設定
在 `TimelineTrigger` 組件的 Inspector 中，你會看到新的 "Fade Settings" 區塊：

```
[Header("Fade Settings")]
- Enable Fade: 是否啟用淡入淡出效果 (預設: true)
- Fade In Duration: 淡入持續時間 (預設: 0.5秒)
- Fade Out Duration: 淡出持續時間 (預設: 0.5秒)  
- Fade In Delay: 淡入前的延遲時間 (預設: 0秒)
- Fade Out Delay: 淡出前的延遲時間 (預設: 0秒)
```

### 2. 參數說明

#### Enable Fade (bool)
- `true`: 啟用黑幕淡入淡出效果
- `false`: 停用效果，Timeline 將正常播放而不會有黑幕

#### Fade In Duration (float)
- Timeline 開始時，從黑幕淡入到透明的時間
- 建議值: 0.3 - 1.0 秒

#### Fade Out Duration (float)  
- Timeline 結束時，從透明淡出到黑幕的時間
- 建議值: 0.3 - 1.0 秒

#### Fade In Delay (float)
- Timeline 開始前等待的時間，然後才開始淡入
- 用於需要額外準備時間的情況

#### Fade Out Delay (float)
- Timeline 結束後等待的時間，然後才開始淡出
- 用於需要額外處理時間的情況

### 3. 工作流程

#### Timeline 開始時:
1. 設定黑幕 (alpha = 1.0)
2. 等待 `fadeInDelay` 時間
3. 開始播放 Timeline
4. 在 `fadeInDuration` 時間內淡入 (alpha: 1.0 → 0.0)

#### Timeline 結束時:
1. 等待 `fadeOutDelay` 時間
2. 在 `fadeOutDuration` 時間內淡出 (alpha: 0.0 → 1.0)
3. 執行 Timeline 清理工作
4. 清除黑幕 (alpha = 0.0)

## 測試功能

### 使用 TimelineFadeTest 腳本
1. 將 `TimelineFadeTest.cs` 附加到場景中的任何 GameObject
2. 設定 `timelineTrigger` 參考到你要測試的 TimelineTrigger
3. 在遊戲中使用以下按鍵測試:
   - `F1`: 測試淡入效果
   - `F2`: 測試淡出效果  
   - `F3`: 測試完整的 Timeline 與淡入淡出

### 手動測試
你也可以直接呼叫 `CameraManager.SetStoryboardCameraAlpha(float alpha)` 來測試:
```csharp
// 設定黑幕
CameraManager.SetStoryboardCameraAlpha(1.0f);

// 設定透明
CameraManager.SetStoryboardCameraAlpha(0.0f);
```

## 技術細節

### 實作位置
功能主要在 `TimelineTrigger.cs` 中實作，包含:
- `FadeInAndStartTimeline()`: 淡入協程
- `FadeOutAndFinishTimeline()`: 淡出協程
- `FinishTimelineCleanup()`: Timeline 清理方法

### 相依性
- `CameraManager.SetStoryboardCameraAlpha()`: 控制黑幕透明度
- `CinemachineStoryboard`: Unity Cinemachine 的 Storyboard 組件

### 效能考量
- 使用 `Time.unscaledDeltaTime` 確保淡入淡出不受遊戲時間縮放影響
- 使用 `WaitForSecondsRealtime` 處理延遲，確保即使遊戲暫停也能正常工作
- 協程在淡入淡出完成後自動結束，不會持續佔用資源

## 故障排除

### 黑幕沒有出現
1. 確認 `CameraManager.storyboard` 不為 null
2. 檢查 `enableFade` 是否設為 true
3. 確認 Timeline 的攝影機設定正確

### 淡入淡出太快/太慢
- 調整 `fadeInDuration` 和 `fadeOutDuration` 參數
- 建議值範圍: 0.1 - 2.0 秒

### Timeline 與淡入淡出不同步
- 檢查 `fadeInDelay` 和 `fadeOutDelay` 設定
- 確認 Timeline 的長度與淡入淡出時間匹配

## 未來擴展

可能的改進方向:
1. 支援不同的淡入淡出曲線 (ease-in, ease-out 等)
2. 支援彩色淡入淡出 (不只是黑色)
3. 支援淡入淡出音效
4. 與 Timeline 軌道整合，支援更複雜的淡入淡出模式
