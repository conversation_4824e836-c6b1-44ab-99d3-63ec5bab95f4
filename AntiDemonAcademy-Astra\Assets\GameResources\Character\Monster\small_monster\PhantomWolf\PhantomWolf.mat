%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-1611920412527283832
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 4
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: PhantomWolf
  m_Shader: {fileID: 4800000, guid: 58105d81543a046429c5d833cb2ac2ef, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords:
  - NOKEWO
  - N_F_EAL_ON
  - N_F_O_ON
  - N_F_RELGI_ON
  - N_F_SS_ON
  - N_F_TRANSAFFSHA
  - N_F_TRANSAFFSHA_ON
  - _ISSPECLIGHT_ON
  - _OUTLINECOLOR_ONLYONE1_ON
  - _OUTLINECOLOR_ONLYONE_ON
  m_LightmapFlags: 2
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 2450
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CameraDepthTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Death_Disolve_Tex:
        m_Texture: {fileID: 2800000, guid: 23dd6a3538a730149a4ed6954ddfa46d, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Disolve_Tex:
        m_Texture: {fileID: 2800000, guid: 9450b76c95d42474593510ffc9e6f564, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Hair_Disolve_Tex:
        m_Texture: {fileID: 2800000, guid: 2f066f54c4985104c82aba65fab70cb3, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _LightMap:
        m_Texture: {fileID: 2800000, guid: ab9414ee7092ca542905ff0eec565c91, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: bc9392d050ddc074cbd1976c5179226b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatcapTex:
        m_Texture: {fileID: 2800000, guid: edc7348b0ceebc349a7bfb266d9f2681, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OntLineColorRamp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SecendTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _1114: 0.01498028
    - _AdditionalLight_intensity: 0.281
    - _AlphaClip: 0
    - _AlphaCutoff: 0.5
    - _AmbientLight: 0.3
    - _Angle_Sun_Y: 1
    - _Blend: 0
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 2
    - _Cutoff: 0.5
    - _Death_Disolve_value: 0
    - _Death_value: 0
    - _DebugEyeDepth: 0
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _Disolve_intensity: 0
    - _Disolve_weapon_intensity: 0
    - _DistanceCutoff: 0.4
    - _DstBlend: 0
    - _EdgeLightColor_Intensity: 1
    - _EdgeLightColor_Intensity1: 1
    - _EdgeLightRange: 0.212
    - _EdgeLightRange1: 0.281
    - _Edge_MaxDistance: 0.206
    - _Edge_width: 0.0047
    - _EnvironmentReflections: 1
    - _FadeInshadow: 0
    - _Float0: 0.56592244
    - _Float1: 1
    - _Float3: 1.34
    - _Float3444: 2.6188114
    - _Float4: 0
    - _Fres_Power: 4.82
    - _Fres_Power1: 2.3
    - _Fres_Scale: 0.846
    - _Fres_Scale1: 0.127
    - _Fres_bias: 0.115
    - _Fres_bias1: 0.245
    - _FresnelBias: 0.11625666
    - _FresnelPower: 3.679396
    - _FresnelScale: 1.5602764
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Ignore_Sun_Y: 0
    - _Inshadow: 0
    - _IsHair: 0
    - _IsHair_outline: 0
    - _IsHair_value: 0
    - _IsHair_value_outline: 0
    - _IsMetal: 0
    - _IsShadow: 0
    - _IsSpecLight: 1
    - _IsWeapon: 0
    - _IsWet: 0
    - _Is_EdgeWithLight: 0
    - _Isdeath: 0
    - _Isdeath_ouline: 0
    - _Keyword0: 0
    - _MaxDistance: 0.3
    - _MaxDistance1: 0.584168
    - _MaxDistance2: 1
    - _MaxRange: 0.091
    - _Metallic: 0
    - _MinRange: 0
    - _NOShadow: 0
    - _Night: 0
    - _NormalOffset: 0.005
    - _OcclusionStrength: 1
    - _OutlineColor_onlyOne: 1
    - _OutlineColor_onlyOne1: 1
    - _OutlineWidth: 0.4
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _RimOffect: 0.008
    - _Selfilluminated_intensity: 0
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _Spec_X_offset: 1
    - _Spec_Y_offset: 0
    - _Spec_intensity: 0
    - _SpecularHighlights: 1
    - _SpecularPow: 15.7
    - _Spread: 0
    - _SrcBlend: 1
    - _Surface: 0
    - _TexChange: 0
    - _Threshold: 0.055
    - _TransAmbient: 0.2
    - _TransDirect: 1
    - _TransNormalDistortion: 0.1
    - _TransScattering: 2
    - _TransShadow: 0.9
    - _Translucency: 1
    - _Transparent_vaule: 0
    - _UniversalForward: 2.5
    - _Width: 0.19
    - _WorkflowMode: 1
    - _ZWrite: 1
    - __dirty: 0
    - _boost_Intensity: 0
    - _boost_Range: 0
    - _disolve_value: 0
    - _disolve_value_Hair: 0
    - _is_EdgeWithLight: 0
    - _isboost_vfx: 0.757
    - _isboost_vfx1: 0
    - _rim_Offset: 0.01498028
    - _rim_offset: 0
    - _rim_range1: 0.69411767
    - _rim_threshold: 0.5874195
    - _rim_width: 0.008
    - _rim_width1: 0.00407
    - _rim_width2: 155.1
    - _rim_wirgd: -0.02
    - _rimlight: 0
    - _wwww: 0.37992185
    m_Colors:
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 0, b: 0, a: 1}
    - _EdgelightColor: {r: 0.8584906, g: 0.8584906, b: 0.8584906, a: 1}
    - _EdgelightColor1: {r: 1, g: 0, b: 0, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FresnelColor: {r: 0, g: 1, b: 0.4274509, a: 1}
    - _OntLineColor: {r: 0.78991634, g: 0.88032264, b: 0.9150943, a: 0}
    - _RimCol: {r: 1, g: 1, b: 1, a: 1}
    - _Selfilluminated_Color: {r: 0, g: 0, b: 0, a: 1}
    - _Shadow_night_Color: {r: 0.7264151, g: 0.7264151, b: 0.7264151, a: 1}
    - _SpecColor: {r: 1, g: 1, b: 1, a: 1}
    - _Spec_Color: {r: 1, g: 0, b: 0, a: 0}
    - _boost_Color: {r: 0.4339623, g: 0.4339623, b: 0.4339623, a: 1}
    - _shadow_sun_Color: {r: 0.8784314, g: 0.8784314, b: 0.8784314, a: 1}
    - _view_vector: {r: 1, g: 0, b: 1, a: 0}
  m_BuildTextureStacks: []
