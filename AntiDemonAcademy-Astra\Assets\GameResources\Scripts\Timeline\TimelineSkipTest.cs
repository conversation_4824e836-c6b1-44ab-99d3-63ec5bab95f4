using UnityEngine;

namespace Studio.Timeline
{
    /// <summary>
    /// Test script to verify timeline skip functionality with fade effects
    /// </summary>
    public class TimelineSkipTest : MonoBehaviour
    {
        [Header("Test Settings")]
        public TimelineTrigger timelineTrigger;
        
        [Space(10)]
        [Header("Manual Test Controls")]
        public KeyCode testSkipTimelineKey = KeyCode.F5;
        public KeyCode testStartTimelineKey = KeyCode.F6;
        
        private void Update()
        {
            // Manual testing controls
            if (UnityEngine.Input.GetKeyDown(testStartTimelineKey))
            {
                TestStartTimeline();
            }
            
            if (UnityEngine.Input.GetKeyDown(testSkipTimelineKey))
            {
                TestSkipTimeline();
            }
        }
        
        /// <summary>
        /// Test starting timeline
        /// </summary>
        public void TestStartTimeline()
        {
            if (timelineTrigger != null && !timelineTrigger.isActive)
            {
                Debug.Log("Starting Timeline for Skip Test");
                timelineTrigger.TriggerDirector();
            }
            else if (timelineTrigger == null)
            {
                Debug.LogWarning("TimelineTrigger reference is not set!");
            }
            else
            {
                Debug.Log("Timeline is already active");
            }
        }
        
        /// <summary>
        /// Test skipping timeline
        /// </summary>
        public void TestSkipTimeline()
        {
            if (timelineTrigger != null && timelineTrigger.isActive)
            {
                Debug.Log("Skipping Timeline - Testing Improved Fade Effect");
                Debug.Log($"Timeline will pause at current frame, fade to black ({timelineTrigger.endFadeToBlackDuration}s), then cleanup");
                timelineTrigger.StopDirector();
            }
            else if (timelineTrigger == null)
            {
                Debug.LogWarning("TimelineTrigger reference is not set!");
            }
            else
            {
                Debug.Log("No active timeline to skip");
            }
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 350, 200));
            GUILayout.Label("Timeline Skip Test Controls:");
            GUILayout.Label($"Press {testStartTimelineKey} to Start Timeline");
            GUILayout.Label($"Press {testSkipTimelineKey} to Skip Timeline");
            
            if (timelineTrigger != null)
            {
                GUILayout.Space(10);
                GUILayout.Label("Timeline Status:");
                GUILayout.Label($"Is Active: {timelineTrigger.isActive}");
                GUILayout.Label($"Is Playing: {timelineTrigger.isPlaying}");
                GUILayout.Label($"Fade Enabled: {timelineTrigger.enableFade}");
                
                if (timelineTrigger.enableFade)
                {
                    GUILayout.Space(5);
                    GUILayout.Label("Fade Settings:");
                    GUILayout.Label($"End Fade To Black: {timelineTrigger.endFadeToBlackDuration}s");
                    GUILayout.Label($"End Fade From Black: {timelineTrigger.endFadeFromBlackDuration}s");
                    GUILayout.Label($"Min Cleanup Duration: {timelineTrigger.minimumCleanupDuration}s");
                }
            }
            else
            {
                GUILayout.Space(10);
                GUILayout.Label("Please assign TimelineTrigger reference!");
            }
            
            GUILayout.EndArea();
        }
    }
}
