using System.Collections;
using UnityEngine;
using UnityEngine.InputSystem;
using Cinemachine;
using EmeraldAI;
using Studio.SysmteArchitecture;
using Studio.Character;

namespace Studio
{
    // 相機模式枚舉
    public enum CameraMode
    {
        Normal,
        LockOn,
        Free,
        FixedPoint,
        Timeline,
    }

    public class CameraManager : Singleton<CameraManager>
    {
        static private Transform player => GameManager.player;
        static public Camera cam;
        static public Transform camTrans;
        static public CinemachineBrain camBrain;
        public int enabledPriority = 60;
        public CinemachineVirtualCamera playerCamera;
        static public CinemachineFramingTransposer framingTransposer;
        static public CinemachinePOV povCamera;
        static public CinemachineStoryboard storyboard;
        static public float keyboardMouseCamYSpeed = 0.1f;
        static public float keyboardMouseCamXSpeed = 0.16f;
        static private float keyboardMouseCamSpeedMultiplier = 20f;
        static private float gamepadCamYSpeed => keyboardMouseCamYSpeed * keyboardMouseCamSpeedMultiplier;
        static private float gamepadCamXSpeed => keyboardMouseCamXSpeed * keyboardMouseCamSpeedMultiplier;
        static public LockOnSystem lockOnSystem;
        static public CameraZoom cameraZoom;
        static public CameraShake cameraShake;

        static private Vector3 _forward;
        static private Vector3 _right;
        static private CameraMode currentMode = CameraMode.Normal;

        static public CameraMode CurrentMode => currentMode;

        static public Vector3 forward {
            get {
                _forward = camTrans.forward;
                _forward.y = 0;
                return _forward.normalized;
            }
        }

        static public Vector3 right {
            get {
                _right = camTrans.right;
                _right.y = 0;
                return _right.normalized;
            }
        }

        #region Sys
        protected override void Initialize()
        {
            base.Initialize();

            cam = GetComponent<Camera>();
            camTrans = transform;
            camBrain = camTrans.GetComponent<CinemachineBrain>();
            cameraZoom = GetComponent<CameraZoom>();
            cameraShake = GetComponent<CameraShake>();
            lockOnSystem = GetComponent<LockOnSystem>();
            framingTransposer = instance.playerCamera.GetCinemachineComponent<CinemachineFramingTransposer>();
            povCamera = instance.playerCamera.GetCinemachineComponent<CinemachinePOV>();
            storyboard = instance.playerCamera.GetComponent<CinemachineStoryboard>();

            povCamera.m_RecenterTarget = CinemachinePOV.RecenterTargetMode.LookAtTargetForward;
            povCamera.m_VerticalRecentering.m_WaitTime = 1.5f;
            povCamera.m_HorizontalRecentering.m_WaitTime = 1.5f;

            // 將相機設置為CameraManager同級
            instance.playerCamera.transform.SetParent(transform.parent);
            EventBusManager.Subscribe<EventBusManager.Event>(EventBusManager.BusType.Global, OnEventBusManagerEvent);
            SubscribeEventBusManagerEvents();
        }

        private void Start() {
            SwitchCameraMode(CameraMode.Normal);
            InitPlayerCamera();
            UpdateCameraSpeed();

            SceneTransitionManager sceneTransMgr = PixelCrushers.SaveSystem.sceneTransitionManager as SceneTransitionManager;
            sceneTransMgr.OnEnterTransitionEnd += delegate {
                GameObject oriPos = GameObject.Find("camera_origin_position");
                if (oriPos != null) {
                    playerCamera.transform.position = oriPos.transform.position;
                    playerCamera.transform.rotation = oriPos.transform.rotation;
                }
                StartCoroutine(InitPlayerCamera());
            };

            InputManager.playerInput.controlsChangedEvent.AddListener(OnControlsChanged);

            LoadSavedSettings();
        }

        private void Update() {
            if (instance.playerCamera.Follow == null)
                StartCoroutine(InitPlayerCamera());
            
            UpdateCameraSpeed();
        }

        protected override void OnDestroy()
        {
            base.OnDestroy();
            UnsubscribeEventBusManagerEvents();
        }
        #endregion

        #region Init
        static private void LoadSavedSettings()
        {
            float savedVertical = PlayerPrefs.GetFloat("CameraVerticalSensitivity", keyboardMouseCamYSpeed);
            float savedHorizontal = PlayerPrefs.GetFloat("CameraHorizontalSensitivity", keyboardMouseCamXSpeed);

            povCamera.m_VerticalAxis.m_MaxSpeed = savedVertical;
            povCamera.m_HorizontalAxis.m_MaxSpeed = savedHorizontal;
        }
        
        static private IEnumerator InitPlayerCamera()
        {
            if (player == null)
                yield break;

            CameraHandler cameraHandler = player.GetComponent<CameraHandler>();
            Transform target = cameraHandler.lookAtObj;

            SetFollowTarget(target);
            SetLookAtTarget(target);

            // 立即移動相機到跟隨目標位置並旋轉到目標前方
            CinemachineVirtualCamera virtualCamera = instance.playerCamera;
            CinemachineTransposer transposer = virtualCamera.GetCinemachineComponent<CinemachineTransposer>();
            Vector3 tempDamping = new Vector3(0, 0, 0);

            // 強制更新 Cinemachine 相機
            // 設置相機位置為跟隨目標的位置
            TeleportCameraToPlayer();

            if (transposer != null)
            {
                // 禁用 Cinemachine 的平滑跟隨和旋轉
                tempDamping = new Vector3(transposer.m_XDamping, transposer.m_YDamping, transposer.m_ZDamping);
                transposer.m_XDamping = 0;
                transposer.m_YDamping = 0;
                transposer.m_ZDamping = 0;
            }

            float angle = Vector3.SignedAngle(Vector3.forward, target.forward, Vector3.up);

            ForwardCamera();
            while (Mathf.Abs(povCamera.m_HorizontalAxis.Value - angle) > 0.1f)
            {
                povCamera.m_HorizontalAxis.Value = angle;
                yield return null;
            }

            // 重置相機設置（可選，視需求而定）
            if (transposer != null)
            {
                transposer.m_XDamping = tempDamping.x;
                transposer.m_YDamping = tempDamping.y;
                transposer.m_ZDamping = tempDamping.z;
            }
        }
        
        static public void SetFollowTarget(Transform target) {
            instance.playerCamera.Follow = target;
        }

        static public void SetLookAtTarget(Transform target) {
            instance.playerCamera.LookAt = target;
        }

        static public void SetFixedCamera(Transform fixedTrans) {
            
        }

        static public void ForwardCamera() {
            if (currentMode != CameraMode.Normal)
                return;

            float angle = Vector3.SignedAngle(Vector3.forward, instance.playerCamera.Follow.forward, Vector3.up);
            if (povCamera != null) {
                // 計算世界前方向量與目標前方向量之間的角度
                // 設置水平軸的值為計算出的角度
                povCamera.m_HorizontalAxis.Value = angle;
                povCamera.m_VerticalAxis.Value = 20;
            }
        }

        static public void ResetCamera() {
            if (currentMode == CameraMode.Normal) {
                if (!povCamera.m_HorizontalRecentering.m_enabled) {
                    instance.StartCoroutine(ResetCameraRoutine());
                }
            }
        }

        // Teleport camera to player
        public static void TeleportCameraToPlayer()
        {
            if (player == null)
                return;
            
            framingTransposer.m_TrackedObjectOffset = Vector3.zero;
            framingTransposer.m_CenterOnActivate = false;
            
            // 重置相機
            instance.playerCamera.PreviousStateIsValid = false;
        }

        static private IEnumerator ResetCameraRoutine() {
            float t = .15f;
            Timer timer = new Timer(t * 2);

            povCamera.m_HorizontalRecentering.m_enabled = true;
            povCamera.m_HorizontalRecentering.m_RecenteringTime = t;
            timer.Go();
            while (!timer.IsTimeUp() && (
                timer.ElapsedTime <= t || 
                (povCamera.m_HorizontalAxis.m_InputAxisValue == 0 && povCamera.m_VerticalAxis.m_InputAxisValue == 0)
            )) {
                timer.Tick(Time.deltaTime);
                povCamera.m_VerticalAxis.Value = Mathf.MoveTowards(povCamera.m_VerticalAxis.Value, 20, 10);
                yield return null;
            }
            povCamera.m_HorizontalRecentering.m_enabled = false;
        }
        #endregion

        #region Mode
        static public void SwitchCameraMode(CameraMode mode)
        {
            currentMode = mode;
            switch (mode)
            {
                case CameraMode.Normal:
                    EnableCamera(instance.playerCamera);
                    SwitchToNormal();
                    SetPovVerticalRange(-70, 80);
                    break;
                case CameraMode.LockOn:
                    EnableCamera(instance.playerCamera);
                    SetPovVerticalRange(-10, 50);
                    break;
                case CameraMode.Timeline:
                    EnableCamera(null);
                    break;
                // 處理其他模式
            }
        }

        static private void EnableCamera(CinemachineVirtualCamera camera)
        {
            instance.playerCamera.Priority = 0;
            if (camera != null)
                camera.Priority = instance.enabledPriority;
        }

        static public void EnableRotation(bool enabled) {

        }

        static private void SwitchToNormal() {
            if (player == null) return;

            CameraHandler handler = player.GetComponent<CameraHandler>();

            instance.playerCamera.Follow = handler.lookAtObj;
            instance.playerCamera.LookAt = handler.lookAtObj;
        }

        static public void SetCameraIgnoreTimeScale(bool ignore) {
            camBrain.m_IgnoreTimeScale = ignore;
            if (ignore)
                camBrain.m_UpdateMethod = CinemachineBrain.UpdateMethod.LateUpdate;
            else
                camBrain.m_UpdateMethod = CinemachineBrain.UpdateMethod.SmartUpdate;
        }

        static private void SetPovVerticalRange(float min, float max) {
            if (min > max) {
                float temp = min;
                min = max;
                max= temp;
            }

            povCamera.m_VerticalAxis.m_MinValue = min;
            povCamera.m_VerticalAxis.m_MaxValue = max;
        }
        #endregion

        #region Controls
        private void OnControlsChanged(PlayerInput playerInput) {
            UpdateCameraSpeed();
        }

        private void UpdateCameraSpeed() {
            switch (InputManager.playerInput.currentControlScheme) {
                case "Keyboard&Mouse":
                    UpdateKeyboardMouseCameraSpeed();
                    break;
                case "Gamepad":
                    UpdateGamepadCameraSpeed();
                    break;
            }
        }

        private void UpdateKeyboardMouseCameraSpeed() {
            povCamera.m_VerticalAxis.m_MaxSpeed = keyboardMouseCamYSpeed;
            povCamera.m_HorizontalAxis.m_MaxSpeed = keyboardMouseCamXSpeed;
        }

        private void UpdateGamepadCameraSpeed() {
            povCamera.m_VerticalAxis.m_MaxSpeed = gamepadCamYSpeed;
            povCamera.m_HorizontalAxis.m_MaxSpeed = gamepadCamXSpeed;
        }

        /// <summary>
        /// 設定垂直軸靈敏度（滑鼠Y軸）
        /// </summary>
        /// <param name="sensitivity">靈敏度值</param>
        static public void SetVerticalSensitivity(float sensitivity) {
            keyboardMouseCamYSpeed = sensitivity;
            if (povCamera != null && InputManager.playerInput.currentControlScheme == "Keyboard&Mouse") {
                povCamera.m_VerticalAxis.m_MaxSpeed = keyboardMouseCamYSpeed;
            }
        }

        /// <summary>
        /// 設定水平軸靈敏度（滑鼠X軸）
        /// </summary>
        /// <param name="sensitivity">靈敏度值</param>
        static public void SetHorizontalSensitivity(float sensitivity) {
            keyboardMouseCamXSpeed = sensitivity;
            if (povCamera != null && InputManager.playerInput.currentControlScheme == "Keyboard&Mouse") {
                povCamera.m_HorizontalAxis.m_MaxSpeed = keyboardMouseCamXSpeed;
            }
        }

        /// <summary>
        /// 獲取當前垂直軸靈敏度
        /// </summary>
        /// <returns>垂直軸靈敏度值</returns>
        static public float GetVerticalSensitivity() {
            return keyboardMouseCamYSpeed;
        }

        /// <summary>
        /// 獲取當前水平軸靈敏度
        /// </summary>
        /// <returns>水平軸靈敏度值</returns>
        static public float GetHorizontalSensitivity() {
            return keyboardMouseCamXSpeed;
        }
        #endregion

        #region Storyboard Camera
        // 0: none, 1: black screen
        public static void SetStoryboardCameraAlpha(float alpha)
        {
            if (storyboard != null)
            {
                storyboard.m_Alpha = alpha;
            }
        }
        #endregion

        private void SubscribeEventBusManagerEvents()
        {
            EventBusManager.Subscribe<CharacterManager.ActiveCharacterLoadedEvent>(EventBusManager.BusType.ActiveScene, OnActiveCharacterLoaded);
        }

        private void UnsubscribeEventBusManagerEvents()
        {
            EventBusManager.Unsubscribe<CharacterManager.ActiveCharacterLoadedEvent>(EventBusManager.BusType.ActiveScene, OnActiveCharacterLoaded);
            EventBusManager.Unsubscribe<EventBusManager.Event>(EventBusManager.BusType.Global, OnEventBusManagerEvent);
        }

        private void OnActiveCharacterLoaded(object sender, CharacterManager.ActiveCharacterLoadedEvent evt)
        {
            StartCoroutine(InitPlayerCamera());
        }

        private void OnEventBusManagerEvent(object sender, EventBusManager.Event evt)
        {
            switch (evt.EventType)
            {
                case EventBusManager.Event.Type.OnActiveSceneSubscriptionsCleared:
                    SubscribeEventBusManagerEvents();
                    break;
            }
        }
    }
}
