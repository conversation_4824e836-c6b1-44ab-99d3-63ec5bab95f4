using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Playables;
using PixelCrushers;
using System.Collections;

namespace Studio.Timeline
{
    [RequireComponent(typeof(TimelineSkipController))]
    public class TimelineTrigger : Interactable
    {
        private bool isTrigger = false;
        private bool isCompleted = false;


        [<PERSON><PERSON>("Director Settings")]
        public PlayableDirector director;

        [Space(10)]
        public bool playOnAwake = false;
        public bool showCursor = false;
        public bool canSkip = true; // 是否可以跳過，如可以則按下ESC顯示跳過提示視窗

        [Header("Trigger Settings")]
        public bool hidePlayerOnTrigger = true;
        public Transform[] hideOnTrigger;
        private Vector3[] oriPosOfHideOnTrigger;

        [Header("Complete Settings")]
        public Transform swapPoint;
        public string sceneName;
        public Transform[] hideOnComplete;
        public StateOnComplete[] stateOnComplete;

        [Header("Fade Settings")]
        [Tooltip("Enable fade in/out effect using storyboard camera")]
        public bool enableFade = true;
        [Tooltip("Duration for fade in at timeline start")]
        public float fadeInDuration = 0.5f;
        [Tooltip("Duration for fade out at timeline end")]
        public float fadeOutDuration = 0.5f;
        [Tooltip("Delay before starting fade in")]
        public float fadeInDelay = 0f;
        [Tooltip("Delay before starting fade out")]
        public float fadeOutDelay = 0f;

        [Space(10)]
        public UnityEvent onTrigger = new UnityEvent();
        public UnityEvent onComplete = new UnityEvent();

        private Transform player => GameManager.player;
        private Vector3 pos;

        private bool isAvailableTrigger => director != null && !isTrigger && SaveSystem.currentAsyncOperation == null;
        public bool isActive => director != null && isTrigger && !isCompleted;
        public bool isPlaying => director != null && director.state == PlayState.Playing;

        [System.Serializable]
        public class StateOnComplete
        {
            public Transform target;
            public Transform spawner;
            public string animation;
        }

        private void Start() {
            if (director == null) {
                Debug.LogWarning("Director is NULL!");
                enabled = false;
                return;
            }

            director.playOnAwake = false;

            oriPosOfHideOnTrigger = new Vector3[hideOnTrigger.Length];

            director.timeUpdateMode = DirectorUpdateMode.UnscaledGameTime;
            director.stopped += OnFinished;

            if (playOnAwake)
            {
                TriggerDirector();
            }
        }

        private void OnTriggerStay(Collider col) {
            if (!col.CompareTag("Player") || triggerType != TriggerType.OnTriggerEnter)
                return;

            if (isAvailableTrigger) {
                TriggerDirector();
            }
        }

        public override void OnUse(GameObject actor)
        {
            if (triggerType != TriggerType.OnUse)
                return;

            if (isAvailableTrigger)
            {
                TriggerDirector();
            }
        }

        public void StopDirector() {
            if (director == null) return;

            director.time = director.duration;
            director.Evaluate();
            director.Stop();
        }

        public void TriggerDirector() {
            TimelineManager.StartDirector(this);

            isTrigger = true;
            isCompleted = false;

            InputManager.showCursor = showCursor;
            InputManager.timelineMap.Enable();

            SetVCamBrain(true);

            onTrigger?.Invoke();

            director.extrapolationMode = DirectorWrapMode.None;

            GameManager.SetPlayerAction(PlayerActionFlags.Timeline, true);
            CameraManager.SwitchCameraMode(CameraMode.Timeline);

            HideObjOnTrigger();
            HidePlayer();

            TimeManager.PauseTime();

            // Start fade in effect before playing timeline
            if (enableFade)
            {
                StartCoroutine(FadeInAndStartTimeline());
            }
            else
            {
                director.time = 0;
                director.Play();
            }
        }

        private void OnFinished(PlayableDirector director) {
            isCompleted = true;

            // Start fade out effect after timeline ends
            if (enableFade)
            {
                StartCoroutine(FadeOutAndFinishTimeline());
            }
            else
            {
                FinishTimelineCleanup();
            }
        }

        private void FinishTimelineCleanup()
        {
            InputManager.showCursor = false;
            InputManager.timelineMap.Disable();

            SetVCamBrain(false);

            GameManager.SetPlayerAction(PlayerActionFlags.Timeline, false);

            TeleportPlayer();
            ShowObjOnComplete();
            HideObjOnComplete();
            HandleStateOnComplete();

            CameraManager.SwitchCameraMode(CameraMode.Normal);
            CameraManager.TeleportCameraToPlayer();
            CameraManager.ForwardCamera();

            if (sceneName != "")
                PixelCrushers.SaveSystem.LoadScene(sceneName);

            gameObject.SetActive(false);

            onComplete?.Invoke();

            TimeManager.ResumeTime();
        }

        private void HidePlayer()
        {
            if (!hidePlayerOnTrigger)
                return;
            
            pos = player.position;
            player.position = new Vector3(0, 9999, 0);
        }

        private void TeleportPlayer()
        {
            if (!player) return;
            
            if (swapPoint) {
                player.position = swapPoint.position;
                player.forward = swapPoint.forward;
            } else if (hidePlayerOnTrigger) {
                player.position = pos;
            }
        }

        private void HandleStateOnComplete() {
            foreach (StateOnComplete state in stateOnComplete)
            {
                if (state.target == null)
                    continue;

                // 設置位置
                if (state.spawner != null)
                {
                    state.target.position = state.spawner.position;
                    state.target.forward = state.spawner.forward;
                }

                // 播放動畫
                if (!string.IsNullOrEmpty(state.animation))
                {
                    Animator animator = state.target.GetComponent<Animator>();
                    if (animator == null)
                    {
                        animator = state.target.GetComponentInChildren<Animator>();
                    }

                    if (animator != null)
                    {
                        animator.Play(state.animation, 0, 0);
                    }
                    else
                    {
                        Debug.LogWarning($"No Animator component found on {state.target.name}");
                    }
                }
            }
        }

        private void SetVCamBrain(bool active) {
            if (director == null) return;

            foreach (var track in director.playableAsset.outputs) {
                if (track.outputTargetType == typeof(Cinemachine.CinemachineBrain)) {
                    director.SetGenericBinding(track.sourceObject, active ? CameraManager.camBrain : null);
                }
            }
        }

        private void HideObjOnTrigger() {
            for (int i = 0; i < hideOnTrigger.Length; i++) {
                oriPosOfHideOnTrigger[i] = hideOnTrigger[i].position;
                hideOnTrigger[i].position = new Vector3(0, 9999, 0);
            }
        }

        private void ShowObjOnComplete() {
            for (int i = 0; i < hideOnTrigger.Length; i++) {
                if (hideOnTrigger[i] != null)
                    hideOnTrigger[i].position = oriPosOfHideOnTrigger[i];
            }
        }

        private void HideObjOnComplete() {
            for (int i = 0; i < hideOnComplete.Length; i++) {
                if (hideOnComplete[i] != null)
                    hideOnComplete[i].position = new Vector3(0, 9999, 0);
            }
        }

        #region Fade Effects
        private IEnumerator FadeInAndStartTimeline()
        {
            // Set initial black screen
            CameraManager.SetStoryboardCameraAlpha(1f);

            // Wait for delay if specified
            if (fadeInDelay > 0f)
            {
                yield return new WaitForSecondsRealtime(fadeInDelay);
            }

            // Start timeline
            director.time = 0;
            director.Play();

            // Fade in (from black to transparent)
            float elapsedTime = 0f;
            while (elapsedTime < fadeInDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(1f, 0f, elapsedTime / fadeInDuration);
                CameraManager.SetStoryboardCameraAlpha(alpha);
                yield return null;
            }

            // Ensure final alpha is 0 (fully transparent)
            CameraManager.SetStoryboardCameraAlpha(0f);
        }

        private IEnumerator FadeOutAndFinishTimeline()
        {
            // Wait for delay if specified
            if (fadeOutDelay > 0f)
            {
                yield return new WaitForSecondsRealtime(fadeOutDelay);
            }

            // Fade out (from transparent to black)
            float elapsedTime = 0f;
            while (elapsedTime < fadeOutDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(0f, 1f, elapsedTime / fadeOutDuration);
                CameraManager.SetStoryboardCameraAlpha(alpha);
                yield return null;
            }

            // Ensure final alpha is 1 (fully black)
            CameraManager.SetStoryboardCameraAlpha(1f);

            // Perform timeline cleanup
            FinishTimelineCleanup();

            // Clear the black screen after cleanup
            CameraManager.SetStoryboardCameraAlpha(0f);
        }
        #endregion
    }
}
