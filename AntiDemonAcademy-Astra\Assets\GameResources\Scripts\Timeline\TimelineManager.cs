using UnityEngine.Playables;
using Studio.UI;

namespace Studio.Timeline
{
    public class TimelineManager : Singleton<TimelineManager>
    {
        public static TimelineTrigger timelineTrigger;
        private static TimelineSkipUI skipUI;

        public delegate void TimelineTriggerDelegate();
        public static event TimelineTriggerDelegate onTimelineTriggerEvent;
        public static event TimelineTriggerDelegate onTimelineStopEvent;

        private void Start() {
            skipUI = UIManager.GetUIElement<TimelineSkipUI>();

            InputManager.playerActionMap.FindAction("Menu").started += delegate {
                if (GameManager.IsOnlyPlayerAction(PlayerActionFlags.Timeline)) {
                    if (timelineTrigger != null && timelineTrigger.canSkip) {
                        skipUI.Toggle();
                    }
                }
            };
        }

        public static void StartDirector(TimelineTrigger trigger) {
            if (trigger == null) return;

            SetTrigger(trigger);

            onTimelineTriggerEvent?.Invoke();
        }

        public static void StopDirector() {
            if (timelineTrigger == null) return;

            timelineTrigger?.StopDirector();
        }

        private static void SetTrigger(TimelineTrigger trigger) {
            if (trigger == null) {
                return;
            }
            
            if (timelineTrigger != null && timelineTrigger.isActive) {
                timelineTrigger.StopDirector();
            }

            timelineTrigger = trigger;
        }

        public static void PauseDirector() {
            if (timelineTrigger?.director?.playableGraph == null) {
                return;
            }

            timelineTrigger?.director?.playableGraph.GetRootPlayable(0).SetSpeed(0);
        }

        public static void ResumeDirector() {
            if (timelineTrigger?.director?.playableGraph == null) {
                return;
            }

            timelineTrigger?.director?.playableGraph.GetRootPlayable(0).SetSpeed(1);
        }

        public static bool IsTrigger() {
            if (timelineTrigger == null) {
                return false;
            }

            return timelineTrigger.isActive;
        }
    }
}