# Timeline 跳過功能優化

## 問題描述
當 Timeline 被跳過或自然結束時，相機會立即回到玩家身上，但此時清理工作（包括角色位置重置、fade 效果等）還沒完成，導致玩家看到不應該看到的畫面，例如：
- 角色還在空中/等待區域/地圖外
- 物件還沒有正確重置位置
- 相機突然切換造成的視覺跳躍

## 解決方案

### 1. 相機切換時機優化
**問題**: 原本相機模式切換在 `FinishTimelineCleanup()` 中進行，當啟用 fade 效果時，這會在黑幕下執行，但相機切換會立即生效。

**解決**: 將相機切換移到 fade 回調中，確保在黑幕完全遮蓋畫面後才進行相機切換。

```csharp
// 在 FinishTimelineWithFade() 中
() => {
    // 在黑幕下執行所有清理操作
    FinishTimelineCleanupWithoutCameraSwitch();
    
    // 在黑幕下切換相機模式，避免視覺跳躍
    CameraManager.SwitchCameraMode(CameraMode.Normal);
    CameraManager.TeleportCameraToPlayer();
    CameraManager.ForwardCamera();
}
```

### 2. 跳過邏輯改進
**問題**: 當使用 `TimelineSkipUI.OnSkip()` 跳過 Timeline 時，畫面會先顯示最後一幀的內容，然後才開始 fade 效果，導致玩家看到不應該看到的畫面。

**解決**: 當啟用 fade 效果時，Timeline 會先暫停在當前幀，立即開始 fade 到黑幕，等黑幕完全出現後才停止 director 並執行清理。

```csharp
public void StopDirector() {
    if (enableFade && !isCompleted) {
        // 暫停 director 在當前幀，而不是立即停止
        director.playableGraph.GetRootPlayable(0).SetSpeed(0);

        // 手動觸發帶有 fade 的停止序列
        isCompleted = true;
        StartCoroutine(StopTimelineWithFade());
    } else {
        // 原始行為（當 fade 停用時）
        director.time = director.duration;
        director.Evaluate();
        director.Stop();
    }
}
```

**新增方法**: `StopTimelineWithFade()`
```csharp
private IEnumerator StopTimelineWithFade() {
    // 先 fade 到黑幕，同時保持 timeline 暫停在當前幀
    yield return CameraManager.FadeToBlack(endFadeToBlackDuration, 0f);

    // 現在畫面是黑的，可以安全地停止 director
    director.Stop();

    // 繼續執行清理和 fade 出來的流程
    // ...
}
```

### 3. 清理操作分離
**問題**: 原本所有清理操作都在 `FinishTimelineCleanup()` 中進行，包括相機切換。

**解決**: 將清理操作分為兩部分：
- `FinishTimelineCleanupWithoutCameraSwitch()`: 執行除相機切換外的所有清理操作
- 相機切換在 fade 回調中單獨執行

## 技術實作細節

### 新增方法
1. **FinishTimelineCleanupWithoutCameraSwitch()**: 執行所有清理操作，但不包括相機切換
2. **修改後的 StopDirector()**: 根據 fade 設定選擇不同的停止邏輯
3. **StopTimelineWithFade()**: 新增的跳過專用方法，先暫停 Timeline，fade 到黑幕，再停止
4. **優化後的 FinishTimelineWithFade()**: 在正確的時機執行相機切換

### 執行流程

#### Timeline 跳過時（啟用 fade）:
1. 用戶按下跳過按鈕
2. `TimelineSkipUI.OnSkip()` → `TimelineManager.StopDirector()` → `TimelineTrigger.StopDirector()`
3. **暫停 Timeline 在當前幀** (`director.playableGraph.GetRootPlayable(0).SetSpeed(0)`)
4. **立即開始淡入到黑幕** (`endFadeToBlackDuration`)
5. **等黑幕完全出現後才停止 director** (`director.Stop()`)
6. 在黑幕下執行所有清理操作和相機切換
7. 確保最小清理時間 (`minimumCleanupDuration`)
8. 從黑幕淡出 (`endFadeFromBlackDuration`)
9. 恢復時間和玩家控制

#### Timeline 自然結束時:
1. Director 播放完成，觸發 `OnFinished`
2. 執行相同的 fade 序列

## 測試

### 使用 TimelineSkipTest 腳本
1. 將 `TimelineSkipTest.cs` 附加到場景中的任何 GameObject
2. 設定 `timelineTrigger` 參考到你要測試的 TimelineTrigger
3. 在遊戲中使用以下按鍵測試:
   - `F6`: 開始 Timeline
   - `F5`: 跳過 Timeline（測試 fade 效果）

### 測試重點
- 確認跳過時不會看到角色在錯誤位置
- 確認相機切換平滑無跳躍
- 確認 fade 效果正常工作
- 確認清理操作完整執行

## 參數建議

### 跳過優化相關參數
- `endFadeToBlackDuration`: 建議設為 0.1-0.3 秒（快速遮蓋）
- `endFadeFromBlackDuration`: 建議設為 0.3-0.5 秒（平滑顯示）
- `minimumCleanupDuration`: 建議設為 0.5-1.0 秒（確保清理完成）

### 注意事項
1. 確保 `enableFade` 已啟用以獲得最佳跳過體驗
2. 如果 Timeline 很短，可以考慮停用跳過功能
3. 測試不同的 fade 時間以找到最適合的視覺效果

## 向後相容性
- 當 `enableFade = false` 時，保持原始的跳過行為
- 現有的 Timeline 設定不需要修改即可使用新功能
- 所有現有的事件和回調仍然正常工作
