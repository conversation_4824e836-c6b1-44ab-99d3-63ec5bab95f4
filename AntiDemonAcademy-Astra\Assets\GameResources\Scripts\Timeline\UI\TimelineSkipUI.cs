using UnityEngine.UI;
using Studio.Timeline;

namespace Studio.UI
{
    public class TimelineSkipUI : UIElement
    {
        public Button firstSelected;

        public override void Show()
        {
            base.Show();
            TimelineManager.PauseDirector();

            if (firstSelected != null)
            {
                firstSelected.Select();
                firstSelected.OnSelect(null);
            }
        }

        public override void Hide()
        {
            base.Hide();
            TimelineManager.ResumeDirector();
        }

        public void Toggle()
        {
            if (isShowing)
            {
                Hide();
            }
            else
            {
                Show();
            }
        }

        public void OnSkip()
        {
            Hide();
            TimelineManager.StopDirector();
        }

        public void OnResume()
        {
            Hide();
            TimelineManager.ResumeDirector();
        }
    }
}
