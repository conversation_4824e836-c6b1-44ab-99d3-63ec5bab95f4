using UnityEngine;
using UnityEngine.Events;
using UnityEngine.Playables;
using PixelCrushers;
using System.Collections;

namespace Studio.Timeline
{
    [RequireComponent(typeof(TimelineSkipController))]
    public class TimelineTrigger : Interactable
    {
        private bool isTrigger = false;
        private bool isCompleted = false;


        [<PERSON><PERSON>("Director Settings")]
        public PlayableDirector director;

        [Space(10)]
        public bool playOnAwake = false;
        public bool showCursor = false;
        public bool canSkip = true; // 是否可以跳過，如可以則按下ESC顯示跳過提示視窗

        [Header("Trigger Settings")]
        public bool hidePlayerOnTrigger = true;
        public Transform[] hideOnTrigger;
        private Vector3[] oriPosOfHideOnTrigger;

        [Header("Complete Settings")]
        public Transform swapPoint;
        public string sceneName;
        public Transform[] hideOnComplete;
        public StateOnComplete[] stateOnComplete;

        [<PERSON><PERSON>("Fade Settings")]
        [Tooltip("Enable fade in/out effect using storyboard camera")]
        public bool enableFade = true;
        [Tooltip("Duration for fade to black at timeline start")]
        public float fadeToBlackDuration = 0.5f;
        [Tooltip("Duration for fade from black at timeline start")]
        public float fadeFromBlackDuration = 0.5f;
        [Tooltip("Delay between fade to black and fade from black at timeline start")]
        public float fadeDelay = 0.5f;
        [Tooltip("Duration for fade to black at timeline end")]
        public float endFadeToBlackDuration = 0.1f;
        [Tooltip("Duration for fade from black at timeline end")]
        public float endFadeFromBlackDuration = 0.5f;
        [Tooltip("Minimum duration for cleanup operations at timeline end")]
        public float minimumCleanupDuration = 1f;

        [Space(10)]
        public UnityEvent onTrigger = new UnityEvent();
        public UnityEvent onComplete = new UnityEvent();

        private Transform player => GameManager.player;
        private Vector3 pos;

        private bool isAvailableTrigger => director != null && !isTrigger && SaveSystem.currentAsyncOperation == null;
        public bool isActive => director != null && isTrigger && !isCompleted;
        public bool isPlaying => director != null && director.state == PlayState.Playing;

        [System.Serializable]
        public class StateOnComplete
        {
            public Transform target;
            public Transform spawner;
            public string animation;
        }

        private void Start() {
            if (director == null) {
                Debug.LogWarning("Director is NULL!");
                enabled = false;
                return;
            }

            director.playOnAwake = false;

            oriPosOfHideOnTrigger = new Vector3[hideOnTrigger.Length];

            director.timeUpdateMode = DirectorUpdateMode.UnscaledGameTime;
            director.stopped += OnFinished;

            if (playOnAwake)
            {
                enableFade = false;
                TriggerDirector();
            }
        }

        private void OnTriggerStay(Collider col) {
            if (!col.CompareTag("Player") || triggerType != TriggerType.OnTriggerEnter)
                return;

            if (isAvailableTrigger) {
                TriggerDirector();
            }
        }

        public override void OnUse(GameObject actor)
        {
            if (triggerType != TriggerType.OnUse)
                return;

            if (isAvailableTrigger)
            {
                TriggerDirector();
            }
        }

        public void StopDirector() {
            if (director == null) return;

            // If fade is enabled, we need to handle the skip differently
            if (enableFade && !isCompleted)
            {
                // Pause the director at current frame instead of stopping immediately
                director.playableGraph.GetRootPlayable(0).SetSpeed(0);

                // Manually trigger the finish sequence with fade
                isCompleted = true;
                
                StartCoroutine(StopTimelineWithFade());
            }
            else
            {
                // Original behavior for when fade is disabled
                director.time = director.duration;
                director.Evaluate();
                director.Stop();
            }
        }

        public void TriggerDirector() {
            TimelineManager.StartDirector(this);

            isTrigger = true;
            isCompleted = false;

            GameManager.SetPlayerAction(PlayerActionFlags.Timeline, true);
            InputManager.showCursor = showCursor;
            InputManager.timelineMap.Enable();

            if (enableFade)
            {
                StartCoroutine(StartTimelineWithFade());
            }
            else
            {
                StartTimelineImmediately();
            }
        }

        private void StartTimelineImmediately()
        {
            SetVCamBrain(true);
            onTrigger?.Invoke();
            director.extrapolationMode = DirectorWrapMode.None;
            CameraManager.SwitchCameraMode(CameraMode.Timeline);
            HideObjOnTrigger();
            HidePlayer();
            TimeManager.PauseTime();

            director.time = 0;
            director.Play();
        }

        private IEnumerator StartTimelineWithFade()
        {
            // Fade to black first
            yield return CameraManager.FadeToBlack(fadeToBlackDuration, 0f);

            // Wait for the specified delay
            if (fadeDelay > 0f)
            {
                yield return new WaitForSecondsRealtime(fadeDelay);
            }

            // Setup timeline components
            SetVCamBrain(true);
            onTrigger?.Invoke();
            director.extrapolationMode = DirectorWrapMode.None;
            CameraManager.SwitchCameraMode(CameraMode.Timeline);
            HideObjOnTrigger();
            HidePlayer();
            TimeManager.PauseTime();

            // Start timeline
            director.time = 0;
            director.Play();

            // Fade from black to show timeline content
            yield return CameraManager.FadeFromBlack(fadeFromBlackDuration, 0f);
        }

        private void OnFinished(PlayableDirector director) {
            isCompleted = true;

            if (enableFade)
            {
                StartCoroutine(FinishTimelineWithFade());
            }
            else
            {
                FinishTimelineCleanup();
                TimeManager.ResumeTime();
            }
        }

        private IEnumerator FinishTimelineWithFade()
        {
            // Fade to black at the beginning of OnFinished
            yield return CameraManager.FadeToBlack(endFadeToBlackDuration, 0f);

            // Perform cleanup with minimum duration tracking
            yield return CameraManager.FadeInOutWithMinimumDuration(
                0f, // No fade in needed (already black)
                endFadeFromBlackDuration, // Fade out duration
                minimumCleanupDuration, // Minimum cleanup duration
                () => {
                    // This callback is called when fade to black is complete (immediately in this case)
                    // Perform all cleanup operations while screen is black to prevent visual glitches
                    FinishTimelineCleanupWithoutCameraSwitch();

                    // Switch camera mode while screen is still black
                    CameraManager.SwitchCameraMode(CameraMode.Normal);
                    CameraManager.TeleportCameraToPlayer();
                    CameraManager.ForwardCamera();
                },
                () =>
                {
                    // This callback is called when fade from black is complete
                    TimeManager.ResumeTime();
                    GameManager.SetPlayerAction(PlayerActionFlags.Timeline, false);
                }
            );
        }

        private void FinishTimelineCleanup()
        {
            // This method is kept for backward compatibility
            // It's used when fade effects are disabled
            FinishTimelineCleanupWithoutCameraSwitch();

            CameraManager.SwitchCameraMode(CameraMode.Normal);
            CameraManager.TeleportCameraToPlayer();
            CameraManager.ForwardCamera();
        }

        private void FinishTimelineCleanupWithoutCameraSwitch()
        {
            // Perform all cleanup operations except camera switching
            InputManager.showCursor = false;
            InputManager.timelineMap.Disable();

            SetVCamBrain(false);

            TeleportPlayer();
            ShowObjOnComplete();
            HideObjOnComplete();
            HandleStateOnComplete();

            if (sceneName != "")
                PixelCrushers.SaveSystem.LoadScene(sceneName);

            gameObject.SetActive(false);

            onComplete?.Invoke();
        }

        private IEnumerator StopTimelineWithFade()
        {
            // First, fade to black while keeping the timeline paused at current frame
            yield return CameraManager.FadeToBlack(endFadeToBlackDuration, 0f);

            // Now that screen is black, we can safely stop the director
            director.stopped -= OnFinished; // Prevent double execution
            director.Stop();
            director.stopped += OnFinished; // Re-register for future use

            // Continue with the rest of the cleanup process
            yield return CameraManager.FadeInOutWithMinimumDuration(
                0f, // No fade in needed (already black)
                endFadeFromBlackDuration, // Fade out duration
                minimumCleanupDuration, // Minimum cleanup duration
                () => {
                    // This callback is called when fade to black is complete (immediately in this case)
                    // Perform all cleanup operations while screen is black to prevent visual glitches
                    FinishTimelineCleanupWithoutCameraSwitch();
                    
                    // Switch camera mode while screen is still black
                    CameraManager.SwitchCameraMode(CameraMode.Normal);
                    CameraManager.TeleportCameraToPlayer();
                    CameraManager.ForwardCamera();
                },
                () =>
                {
                    // This callback is called when fade from black is complete
                    TimeManager.ResumeTime();
                    GameManager.SetPlayerAction(PlayerActionFlags.Timeline, false);
                }
            );
        }

        private void HidePlayer()
        {
            if (!hidePlayerOnTrigger)
                return;
            
            pos = player.position;
            player.position = new Vector3(0, 9999, 0);
        }

        private void TeleportPlayer()
        {
            if (!player) return;
            
            if (swapPoint) {
                player.position = swapPoint.position;
                player.forward = swapPoint.forward;
            } else if (hidePlayerOnTrigger) {
                player.position = pos;
            }
        }

        private void HandleStateOnComplete() {
            foreach (StateOnComplete state in stateOnComplete)
            {
                if (state.target == null)
                    continue;

                // 設置位置
                if (state.spawner != null)
                {
                    state.target.position = state.spawner.position;
                    state.target.forward = state.spawner.forward;
                }

                // 播放動畫
                if (!string.IsNullOrEmpty(state.animation))
                {
                    Animator animator = state.target.GetComponent<Animator>();
                    if (animator == null)
                    {
                        animator = state.target.GetComponentInChildren<Animator>();
                    }

                    if (animator != null)
                    {
                        animator.Play(state.animation, 0, 0);
                    }
                    else
                    {
                        Debug.LogWarning($"No Animator component found on {state.target.name}");
                    }
                }
            }
        }

        private void SetVCamBrain(bool active) {
            if (director == null) return;

            foreach (var track in director.playableAsset.outputs) {
                if (track.outputTargetType == typeof(Cinemachine.CinemachineBrain)) {
                    director.SetGenericBinding(track.sourceObject, active ? CameraManager.camBrain : null);
                }
            }
        }

        private void HideObjOnTrigger() {
            for (int i = 0; i < hideOnTrigger.Length; i++) {
                oriPosOfHideOnTrigger[i] = hideOnTrigger[i].position;
                hideOnTrigger[i].position = new Vector3(0, 9999, 0);
            }
        }

        private void ShowObjOnComplete() {
            for (int i = 0; i < hideOnTrigger.Length; i++) {
                if (hideOnTrigger[i] != null)
                    hideOnTrigger[i].position = oriPosOfHideOnTrigger[i];
            }
        }

        private void HideObjOnComplete() {
            for (int i = 0; i < hideOnComplete.Length; i++) {
                if (hideOnComplete[i] != null)
                    hideOnComplete[i].position = new Vector3(0, 9999, 0);
            }
        }


    }
}
