%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: boxing_04
  m_Shader: {fileID: 4800000, guid: 6eb86e4931b506a408fd1477edec61ea, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - ADDITIVECONFIG_ON
  - ALPHACUTOFF_ON
  - MASK_ON
  - PREMULTIPLYCOLOR_ON
  - SHAPE2_ON
  m_InvalidKeywords:
  - _COVERAGEOVERLAYTYPE_WORLD_NORMAL
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: 3000
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorRampTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ColorRampTexGradient:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoverageAlbedo:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoverageMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _CoverageNormal:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistNormalMap:
        m_Texture: {fileID: 2800000, guid: 06907d08092c41f4aa8b5b3cbe5f9fa2, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DistortTex:
        m_Texture: {fileID: 2800000, guid: fcb027caad6963b4a80539bbce0d10e9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeBurnTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _FadeTex:
        m_Texture: {fileID: 2800000, guid: 854b25bc08516364da5a786b1cad247e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Flow:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _GlowTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: fb665a7b8ab783e46974269b57443ee0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Mask:
        m_Texture: {fileID: 2800000, guid: cb8216d94b13a0d4981842e28106b9d4, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskTex:
        m_Texture: {fileID: 2800000, guid: ad5da642b28ff2d4d913e410c5dcc0f4, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Noise:
        m_Texture: {fileID: 2800000, guid: fcb027caad6963b4a80539bbce0d10e9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape1MaskTex:
        m_Texture: {fileID: 2800000, guid: db8fa36fcfe9738418db54ee0fe17fdf, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape2DistortTex:
        m_Texture: {fileID: 2800000, guid: fcb027caad6963b4a80539bbce0d10e9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape2Tex:
        m_Texture: {fileID: 2800000, guid: fe58d421162ef414a8f49ddcf11b8cf8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape3DistortTex:
        m_Texture: {fileID: 2800000, guid: fcb027caad6963b4a80539bbce0d10e9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Shape3Tex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShapeDistortTex:
        m_Texture: {fileID: 2800000, guid: fcb027caad6963b4a80539bbce0d10e9, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SurfaceAlbedo:
        m_Texture: {fileID: 2800000, guid: 8b23a6ac76152ce4eada51c96ed07664, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SurfaceNormal:
        m_Texture: {fileID: 2800000, guid: 3496416a4a8ec0241a519984b99b05d0, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _TrailWidthGradient:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _VertOffsetTex:
        m_Texture: {fileID: 2800000, guid: 48da24d771916524899d36e4bfe7f762, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Addnoise: 0
    - _Alpha: 1
    - _AlphaClip: 0
    - _AlphaCutoff: 0.5
    - _AlphaCutoffValue: 0.25
    - _AlphaFadeAmount: -0.1
    - _AlphaFadePow: 1
    - _AlphaFadeSmooth: 0.075
    - _AlphaStepMax: 0.075
    - _AlphaStepMin: 0
    - _Blend: 0
    - _BlendNormals: 0
    - _BumpScale: 1
    - _COVERAGE: 0
    - _CamDistFadeStepMax: 100
    - _CamDistFadeStepMin: 0
    - _CamDistProximityFade: 0
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _ColorGradingMidPoint: 0.5
    - _ColorMask: 15
    - _ColorRampBlend: 1
    - _ColorRampLuminosity: 0
    - _CoverageContrast: 0.3
    - _CoverageFade: 0.5
    - _CoverageLevel: 0
    - _CoverageOverlayType: 0
    - _CoverageSmoothness: 0
    - _CoverageThicknessLevel: 1
    - _Cull: 2
    - _CullingOption: 0
    - _Cutoff: 0.5
    - _DebugShape: 1
    - _DepthGlow: 1
    - _DepthGlowDist: 0.5
    - _DepthGlowGlobal: 1
    - _DepthGlowPow: 1
    - _Depthpower: 0.2
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DistortAmount: 0.5
    - _DistortTexXSpeed: 5
    - _DistortTexYSpeed: 5
    - _DistortionBlend: 1
    - _DistortionPower: 10
    - _DistortionScrollXSpeed: 0
    - _DistortionScrollYSpeed: 0
    - _Distortionpower: 0.2
    - _DstBlend: 0
    - _DstMode: 10
    - _EditorDrawers: 61
    - _Emission: 2
    - _EnvironmentReflections: 1
    - _FadeAmount: -0.1
    - _FadeBurnGlow: 5
    - _FadeBurnWidth: 0.01
    - _FadePower: 1
    - _FadeScrollXSpeed: 0
    - _FadeScrollYSpeed: 0
    - _FadeTransition: 0.075
    - _Fresnelpower: 3
    - _Fresnelscale: 3
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _Glow: 0
    - _GlowGlobal: 1
    - _HandDrawnAmount: 10
    - _HandDrawnSpeed: 5
    - _HsvBright: 1
    - _HsvSaturation: 1
    - _HsvShift: 180
    - _LightAmount: 0
    - _MaskPow: 1
    - _Metallic: 0
    - _OcclusionStrength: 1
    - _OffsetSh1: 1
    - _OffsetSh2: 1
    - _OffsetSh3: 1
    - _Parallax: 0.005
    - _PixelateSize: 32
    - _PosterizeNumColors: 5
    - _QueueOffset: 0
    - _RandomSh1Mult: 1
    - _RandomSh2Mult: 1
    - _RandomSh3Mult: 1
    - _ReceiveShadows: 1
    - _RenderingMode: 3
    - _RimAddAmount: 1
    - _RimBias: 0
    - _RimErodesAlpha: 0
    - _RimIntensity: 1
    - _RimPower: 5
    - _RimScale: 1
    - _RoundWaveSpeed: 2
    - _RoundWaveStrength: 0.7
    - _SNOW: 0
    - _ScreenUvSh2DistScale: 1
    - _ScreenUvSh3DistScale: 1
    - _ScreenUvShDistScale: 1
    - _Sh1BlendOffset: 0
    - _Sh2BlendOffset: 0
    - _Sh3BlendOffset: 0
    - _ShadowAmount: 0.4
    - _ShadowStepMax: 1
    - _ShadowStepMin: 0
    - _ShakeUvSpeed: 20
    - _ShakeUvX: 5
    - _ShakeUvY: 4
    - _Shape1MaskPow: 1
    - _Shape2AlphaWeight: 2
    - _Shape2Brightness: 0
    - _Shape2ColorWeight: 2
    - _Shape2Contrast: 1
    - _Shape2DistortAmount: 0.5
    - _Shape2DistortXSpeed: 0.1
    - _Shape2DistortYSpeed: 0.1
    - _Shape2RotationOffset: 0
    - _Shape2RotationSpeed: 0
    - _Shape2XSpeed: 0
    - _Shape2YSpeed: 0
    - _Shape3AlphaWeight: 2
    - _Shape3Brightness: 0
    - _Shape3ColorWeight: 2
    - _Shape3Contrast: 1
    - _Shape3DistortAmount: 0.5
    - _Shape3DistortXSpeed: 0.1
    - _Shape3DistortYSpeed: 0.1
    - _Shape3RotationOffset: 0
    - _Shape3RotationSpeed: 0
    - _Shape3XSpeed: 0
    - _Shape3YSpeed: 0
    - _ShapeAlphaWeight: 1
    - _ShapeBrightness: 0
    - _ShapeColorWeight: 1
    - _ShapeContrast: 1
    - _ShapeDistortAmount: 0.5
    - _ShapeDistortXSpeed: 0.1
    - _ShapeDistortYSpeed: 0.1
    - _ShapeRotationOffset: 0
    - _ShapeRotationSpeed: 0
    - _ShapeXSpeed: 0
    - _ShapeYSpeed: 0
    - _Smoothness: 0.5
    - _SmoothnessTextureChannel: 0
    - _SnowAmount: 0.5
    - _SnowFade: 0.5
    - _SoftFactor: 0.5
    - _SpecularHighlights: 1
    - _SrcBlend: 1
    - _SrcMode: 1
    - _Surface: 0
    - _SurfaceSmoothness: 0
    - _TessEdgeLength: 16
    - _TessMax: 25
    - _TessMaxDisp: 25
    - _TessMin: 10
    - _TessPhongStrength: 0.5
    - _TessValue: 16
    - _TextureScrollXSpeed: 1
    - _TextureScrollYSpeed: 0
    - _Texturesopacity: 1
    - _TimingSeed: 0
    - _TrailWidthPower: 1
    - _TransAmbient: 0.1
    - _TransDirect: 0.9
    - _TransNormal: 0.5
    - _TransScattering: 2
    - _TransShadow: 0.5
    - _TransStrength: 1
    - _TransmissionShadow: 0.5
    - _TwistUvAmount: 1
    - _TwistUvPosX: 0.5
    - _TwistUvPosY: 0.5
    - _TwistUvRadius: 0.75
    - _Useonlycolor: 0
    - _VertOffsetAmount: 0.5
    - _VertOffsetPower: 1
    - _VertOffsetTexXSpeed: 0.1
    - _VertOffsetTexYSpeed: 0.1
    - _WaveAmount: 7
    - _WaveSpeed: 10
    - _WaveStrength: 7.5
    - _WaveX: 0
    - _WaveY: 0.5
    - _WorkflowMode: 1
    - _ZTestMode: 4
    - _ZWrite: 1
    m_Colors:
    - _BackFaceTint: {r: 0.5, g: 0.5, b: 0.5, a: 1}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _ColorGradingDark: {r: 1, g: 1, b: 1, a: 1}
    - _ColorGradingLight: {r: 1, g: 1, b: 1, a: 1}
    - _ColorGradingMiddle: {r: 1, g: 1, b: 1, a: 1}
    - _CoverageColor: {r: 0, g: 0, b: 0, a: 0}
    - _DepthGlowColor: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _FadeBurnColor: {r: 1, g: 1, b: 0, a: 1}
    - _FrontFaceTint: {r: 1, g: 1, b: 1, a: 1}
    - _GlowColor: {r: 1, g: 1, b: 1, a: 1}
    - _LightColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 1}
    - _Shape2Color: {r: 1, g: 0.9395778, b: 0.36320752, a: 1}
    - _Shape3Color: {r: 1, g: 1, b: 1, a: 1}
    - _ShapeColor: {r: 3.6293147, g: 3.455337, b: 3.2013295, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _SpeedMainTexUVNoiseZW: {r: 0, g: 0, b: 0, a: 0}
    - _SurfaceColor: {r: 1, g: 1, b: 1, a: 0}
  m_BuildTextureStacks: []
--- !u!114 &2838391788756868954
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 4
