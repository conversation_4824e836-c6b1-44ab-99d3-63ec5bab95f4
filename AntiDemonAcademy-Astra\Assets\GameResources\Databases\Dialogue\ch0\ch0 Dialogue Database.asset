%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 935899b62f48ae5498594680ed17d133, type: 3}
  m_Name: ch0 Dialogue Database
  m_EditorClassIdentifier: 
  version: 
  author: 
  description: 
  globalUserScript: 
  emphasisSettings:
  - color: {r: 1, g: 1, b: 1, a: 1}
    bold: 0
    italic: 0
    underline: 0
  - color: {r: 1, g: 0, b: 0, a: 1}
    bold: 0
    italic: 0
    underline: 0
  - color: {r: 0, g: 1, b: 0, a: 1}
    bold: 0
    italic: 0
    underline: 0
  - color: {r: 0, g: 0, b: 1, a: 1}
    bold: 0
    italic: 0
    underline: 0
  baseID: 1
  actors:
  - id: 1
    fields:
    - title: Name
      value: Player
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: NodeColor
      value: Blue
      type: 0
      typeString: CustomFieldType_Text
    - title: Display Name
      value: Player
      type: 0
      typeString: CustomFieldType_Text
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 2
    fields:
    - title: Name
      value: Takeshi Ise
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: NodeColor
      value: '#8787e2ff'
      type: 0
      typeString: CustomFieldType_Text
    - title: Display Name
      value: Takeshi Ise
      type: 0
      typeString: CustomFieldType_Text
    - title: Display Name en
      value: Takeshi Ise
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-tw
      value: "\u6B66\u4F0A\u52E2"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-cn
      value: "\u6B66\u4F0A\u52BF"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name jp
      value: "\u6B66\u4F0A\u52E2"
      type: 4
      typeString: CustomFieldType_Localization
    portrait: {fileID: 2800000, guid: 40779ec037b69784dbaffc2422266dfb, type: 3}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 3
    fields:
    - title: Name
      value: Yumiko Ise
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: NodeColor
      value: '#f7bee9ff'
      type: 0
      typeString: CustomFieldType_Text
    - title: Display Name
      value: Yumiko Ise
      type: 0
      typeString: CustomFieldType_Text
    - title: Display Name en
      value: Yumiko Ise
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-tw
      value: "\u7531\u7F8E\u5B50\u4F0A\u52E2"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-cn
      value: "\u7531\u7F8E\u5B50\u4F0A\u52BF"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name jp
      value: "\u7531\u7F8E\u5B50\u4F0A\u52E2"
      type: 4
      typeString: CustomFieldType_Localization
    portrait: {fileID: 2800000, guid: e2f4e602f5cf79a428dc134be1a686cd, type: 3}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  - id: 4
    fields:
    - title: Name
      value: New Actor 4
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: IsPlayer
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: NodeColor
      value: Gray
      type: 0
      typeString: CustomFieldType_Text
    - title: Display Name
      value: New Actor 4
      type: 0
      typeString: CustomFieldType_Text
    portrait: {fileID: 0}
    spritePortrait: {fileID: 0}
    alternatePortraits: []
    spritePortraits: []
  items:
  - id: 1
    fields:
    - title: Name
      value: "\u4E0A\u9999\u6E96\u5099"
      type: 0
      typeString: CustomFieldType_Text
    - title: Display Name en
      value: Preparation of incense
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-tw
      value: "\u4E0A\u9999\u6E96\u5099"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-cn
      value: "\u4E0A\u9999\u51C6\u5907"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name jp
      value: "\u304A\u7DDA\u9999\u306E\u6E96\u5099"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Description en
      value: Prepare for the ritual offerings.
      type: 4
      typeString: CustomFieldType_Localization
    - title: Description zh-tw
      value: "\u505A\u597D\u796D\u62DC\u7684\u6E96\u5099"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Description zh-cn
      value: "\u505A\u597D\u796D\u62DC\u7684\u51C6\u5907"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Description jp
      value: "\u304A\u4F9B\u3048\u306E\u6E96\u5099\u3092\u3059\u308B\u3002"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Success Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Failure Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Is Item
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: 
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Trackable
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Track
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Entry Count
      value: 3
      type: 1
      typeString: CustomFieldType_Number
    - title: Entry 2 State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Entry 2
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry 2 en
      value: Find the incense on the cabinet
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 zh-tw
      value: "\u5C0B\u627E\u6AC3\u5B50\u4E0A\u7684\u9999"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 zh-cn
      value: "\u5BFB\u627E\u67DC\u5B50\u4E0A\u7684\u9999"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 jp
      value: "\u672C\u68DA\u306E\u4E0A\u306E\u7DDA\u9999\u3092\u63A2\u3059"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry 1 State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Entry 1 en
      value: Go home
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1 zh-tw
      value: "\u524D\u5F80\u5BB6\u88E1"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1 zh-cn
      value: "\u524D\u5F80\u5BB6\u91CC"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1 jp
      value: "\u5BB6\u306B\u5E30\u308C"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name
      value: "\u4E0A\u9999\u6E96\u5099"
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry 3 State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Entry 3
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry 3 en
      value: Talk to Stepmom
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 3 zh-tw
      value: "\u8DDF\u5ABD\u5ABD\u5C0D\u8A71"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 3 zh-cn
      value: "\u8DDF\u5988\u5988\u5BF9\u8BDD"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 3 jp
      value: "\u6BCD\u3055\u3093\u3068\u8A71\u3059"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Group
      value: ML
      type: 0
      typeString: CustomFieldType_Text
  - id: 2
    fields:
    - title: Name
      value: "\u6E05\u9664\u53F2\u840A\u59C6"
      type: 0
      typeString: CustomFieldType_Text
    - title: Display Name en
      value: Clear Slime
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-tw
      value: "\u6E05\u9664\u53F2\u840A\u59C6"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-cn
      value: "\u6E05\u9664\u53F2\u83B1\u59C6"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name jp
      value: "\u30B9\u30E9\u30A4\u30E0\u3092\u5012\u3059"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Description en
      value: Stepmom asked me to clear out the slimes outside.
      type: 4
      typeString: CustomFieldType_Localization
    - title: Description zh-tw
      value: "\u5ABD\u5ABD\u62DC\u8A17\u6211\u6E05\u9664\u5916\u9762\u7684\u53F2\u840A\u59C6\u3002"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Description zh-cn
      value: "\u5988\u5988\u62DC\u6258\u6211\u6E05\u9664\u5916\u9762\u7684\u53F2\u83B1\u59C6\u3002"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Description jp
      value: "\u6BCD\u3055\u3093\u3001\u5916\u306E\u30B9\u30E9\u30A4\u30E0\u3092\u9000\u6CBB\u3057\u3066\u3063\u3066\u983C\u307E\u308C\u305F\u3093\u3060\u3002"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Success Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Failure Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Is Item
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: 
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Trackable
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Track
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Display Name
      value: "\u6E05\u9664\u53F2\u840A\u59C6"
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry Count
      value: 2
      type: 1
      typeString: CustomFieldType_Number
    - title: Entry 1 State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Entry 1
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry 1 en
      value: Clear Slime [var=slimeKilled]/3
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1 zh-tw
      value: "\u6E05\u9664\u53F2\u840A\u59C6 [var=slimeKilled]/3"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1 zh-cn
      value: "\u6E05\u9664\u53F2\u83B1\u59C6 [var=slimeKilled]/3"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1 jp
      value: "\u30B9\u30E9\u30A4\u30E0\u3092\u5012\u3059 [var=slimeKilled]/3"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry 2 State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Entry 2 en
      value: Check the exorcism table
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 zh-tw
      value: "\u6AA2\u67E5\u9A45\u9B54\u53F0"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 zh-cn
      value: "\u68C0\u67E5\u9A71\u9B54\u53F0"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 jp
      value: "\u9A45\u9B54\u53F0\u3092\u8ABF\u3079\u308B"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Group
      value: ML
      type: 0
      typeString: CustomFieldType_Text
  - id: 3
    fields:
    - title: Name
      value: "\u524D\u5F80\u5893\u5712"
      type: 0
      typeString: CustomFieldType_Text
    - title: Display Name en
      value: Go to the cemetery
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-tw
      value: "\u524D\u5F80\u5893\u5712"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name zh-cn
      value: "\u524D\u5F80\u5893\u56ED"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Display Name jp
      value: "\u5893\u5730\u3078\u884C\u304F"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Description en
      value: 
      type: 4
      typeString: CustomFieldType_Localization
    - title: Description zh-tw
      value: 
      type: 4
      typeString: CustomFieldType_Localization
    - title: Description zh-cn
      value: 
      type: 4
      typeString: CustomFieldType_Localization
    - title: Description jp
      value: 
      type: 4
      typeString: CustomFieldType_Localization
    - title: Success Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Failure Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Is Item
      value: False
      type: 2
      typeString: CustomFieldType_Boolean
    - title: 
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Trackable
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Track
      value: True
      type: 2
      typeString: CustomFieldType_Boolean
    - title: Display Name
      value: "\u524D\u5F80\u5893\u5712"
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry Count
      value: 2
      type: 1
      typeString: CustomFieldType_Number
    - title: Entry 1 State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Entry 1
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry 1 en
      value: Go to the cemetery
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1 zh-tw
      value: "\u524D\u5F80\u5893\u5712"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1 zh-cn
      value: "\u524D\u5F80\u5893\u56ED"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 1 jp
      value: "\u5893\u5730\u3078\u884C\u304F"
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 State
      value: unassigned
      type: 0
      typeString: CustomFieldType_QuestState
    - title: Entry 2
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Entry 2 en
      value: 
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 zh-tw
      value: 
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 zh-cn
      value: 
      type: 4
      typeString: CustomFieldType_Localization
    - title: Entry 2 jp
      value: 
      type: 4
      typeString: CustomFieldType_Localization
    - title: Group
      value: ML
      type: 0
      typeString: CustomFieldType_Text
  locations: []
  variables:
  - id: 1
    fields:
    - title: Name
      value: slimeKilled
      type: 0
      typeString: CustomFieldType_Text
    - title: Initial Value
      value: 0
      type: 1
      typeString: CustomFieldType_Number
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
  conversations:
  - id: 1
    fields:
    - title: Title
      value: ch0_msg_01
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: -1
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: None()
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 0
        destinationConversationID: 1
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 113.89935
        y: 55.940308
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u98DF\u6750\u6E96\u5099\u5F97\u5DEE\u4E0D\u591A\u4E86,\u662F\u6642\u5019\u8A72\u56DE\u53BB\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The ingredients are almost ready. It's time to go back.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u98DF\u6750\u6E96\u5099\u5F97\u5DEE\u4E0D\u591A\u4E86,\u662F\u6642\u5019\u8A72\u56DE\u53BB\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u98DF\u6750\u51C6\u5907\u5F97\u5DEE\u4E0D\u591A\u4E86,\u662F\u65F6\u5019\u8BE5\u56DE\u53BB\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u98DF\u6750\u304C\u307B\u307C\u63C3\u3063\u305F\u306E\u3067\u3001\u305D\u308D\u305D\u308D\u5E30\u308A\u307E\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 1
        destinationConversationID: 1
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 118.7805
        y: 125.31305
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u8981\u662F\u8B93\u5ABD\u5ABD\u64D4\u5FC3\u53EF\u5C31\u4E0D\u597D\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Don't make Mom worry.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u8981\u662F\u8B93\u5ABD\u5ABD\u64D4\u5FC3\u53EF\u5C31\u4E0D\u597D\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u8981\u662F\u8BA9\u5988\u5988\u62C5\u5FC3\u53EF\u5C31\u4E0D\u597D\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6BCD\u3055\u3093\u306B\u5FC3\u914D\u3092\u304B\u3051\u305F\u304F\u306A\u3044\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 116.829285
        y: 198.48782
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u7E3D\u611F\u89BA\u6700\u8FD1\u7684\u53F2\u840A\u59C6\u8B8A\u591A\u4E86......"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: There's been a ton of slimes around lately......
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u7E3D\u611F\u89BA\u6700\u8FD1\u7684\u53F2\u840A\u59C6\u8B8A\u591A\u4E86......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u603B\u611F\u89C9\u6700\u8FD1\u7684\u53F2\u83B1\u59C6\u53D8\u591A\u4E86......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6700\u8FD1\u30B9\u30E9\u30A4\u30E0\u304C\u5897\u3048\u305F\u6C17\u304C\u3059\u308B\u3051\u3069......"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 3
        destinationConversationID: 1
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 308.11322
        y: 212.8728
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u96E3\u9053\u53C8\u6709\u4EC0\u9EBC\u4E8B\u8981\u767C\u751F\u4E86\u55CE?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I've got a bad feeling about this. Something's up.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u96E3\u9053\u53C8\u6709\u4EC0\u9EBC\u4E8B\u8981\u767C\u751F\u4E86\u55CE?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u96BE\u9053\u53C8\u6709\u4EC0\u4E48\u4E8B\u8981\u53D1\u751F\u4E86\u5417?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u4F55\u304B\u8D77\u3053\u308B\u524D\u5146\u306A\u306E\u304B\u306A\u2026"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 309.0841
        y: 264.85217
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u54E5\u5E03\u6797!\u70BA\u4EC0\u9EBC\u9019\u88E1\u6703\u51FA\u73FE\u54E5\u5E03\u6797!"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Goblin! Why are there goblins here!?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u54E5\u5E03\u6797!\u70BA\u4EC0\u9EBC\u9019\u88E1\u6703\u51FA\u73FE\u54E5\u5E03\u6797!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u54E5\u5E03\u6797!\u4E3A\u4EC0\u4E48\u8FD9\u91CC\u4F1A\u51FA\u73B0\u54E5\u5E03\u6797!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u30B4\u30D6\u30EA\u30F3\uFF01\uFF1F\u306A\u3093\u3067\u3053\u3093\u306A\u3068\u3053\u308D\u306B\u30B4\u30D6\u30EA\u30F3\u304C\u3044\u308B\u3093\u3060\uFF01\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 524.90027
        y: 201.34402
        width: 160
        height: 30
    - id: 9
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6211\u660E\u660E\u5DF2\u7D93\u6BB2\u6EC510\u516C\u91CC\u7BC4\u570D\u7684\u6240\u6709\u5DE2\u7A74\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I'm pretty sure I wiped out all the nearby goblin nests.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6211\u660E\u660E\u5DF2\u7D93\u6BB2\u6EC510\u516C\u91CC\u5167\u7684\u6240\u6709\u54E5\u5E03\u6797\u5DE2\u7A74\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u6211\u660E\u660E\u5DF2\u7ECF\u6B7C\u706D10\u516C\u91CC\u5167\u7684\u6240\u6709\u54E5\u5E03\u6797\u5DE2\u7A74\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "10\u30AD\u30ED\u570F\u5185\u306E\u30B4\u30D6\u30EA\u30F3\u306E\u5DE3\u306F\u5168\u6EC5\u3055\u305B\u305F\u306F\u305A\u306A\u306E\u306B\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 9
        destinationConversationID: 1
        destinationDialogueID: 11
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 744.187
        y: 164.95209
        width: 160
        height: 30
    - id: 10
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u4E0D\u60F3\u4E86,\u9084\u662F\u5148\u56DE\u53BB\u78BA\u8A8D\u5ABD\u5ABD\u7684\u5B89\u5168\u5427\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Forget it. Gotta make sure Mom is okay.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u4E0D\u60F3\u4E86,\u9084\u662F\u5148\u56DE\u53BB\u78BA\u8A8D\u5ABD\u5ABD\u7684\u5B89\u5168\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u4E0D\u60F3\u4E86,\u8FD8\u662F\u5148\u56DE\u53BB\u786E\u8BA4\u5988\u5988\u7684\u5B89\u5168\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3068\u306B\u304B\u304F\u3001\u307E\u305A\u306F\u6BCD\u3055\u3093\u306E\u5B89\u5426\u3092\u78BA\u8A8D\u3057\u3088\u3046\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 767.2267
        y: 299.23453
        width: 160
        height: 30
    - id: 11
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9019\u5E7E\u96BB\u54E5\u5E03\u6797\u7A76\u7ADF\u662F\u5F9E\u4F55\u800C\u4F86?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Where'd these goblins come from?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9019\u5E7E\u96BB\u54E5\u5E03\u6797\u7A76\u7ADF\u662F\u5F9E\u4F55\u800C\u4F86?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u8FD9\u51E0\u53EA\u54E5\u5E03\u6797\u7A76\u7ADF\u662F\u4ECE\u4F55\u800C\u6765?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3053\u306E\u30B4\u30D6\u30EA\u30F3\u305F\u3061\u306F\u4E00\u4F53\u3069\u3053\u304B\u3089\u6765\u305F\u3093\u3060\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 1
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 1
        originDialogueID: 11
        destinationConversationID: 1
        destinationDialogueID: 10
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 746.09326
        y: 224.47119
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 0.73000026
  - id: 2
    fields:
    - title: Title
      value: ch0_msg_02
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: -1
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: None()
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 2
        originDialogueID: 0
        destinationConversationID: 2
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 160
        y: 29
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u7E3D\u611F\u89BA\u6700\u8FD1\u7684\u53F2\u840A\u59C6\u8B8A\u591A\u4E86......"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: There's been a ton of slimes around lately......
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u7E3D\u611F\u89BA\u6700\u8FD1\u7684\u53F2\u840A\u59C6\u8B8A\u591A\u4E86......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u603B\u611F\u89C9\u6700\u8FD1\u7684\u53F2\u83B1\u59C6\u53D8\u591A\u4E86......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6700\u8FD1\u30B9\u30E9\u30A4\u30E0\u304C\u5897\u3048\u305F\u6C17\u304C\u3059\u308B\u3051\u3069......"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 2
        originDialogueID: 1
        destinationConversationID: 2
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 160
        y: 77
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u96E3\u9053\u53C8\u6709\u4EC0\u9EBC\u4E8B\u8981\u767C\u751F\u4E86\u55CE?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I've got a bad feeling about this. Something's up.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u96E3\u9053\u53C8\u6709\u4EC0\u9EBC\u4E8B\u8981\u767C\u751F\u4E86\u55CE?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u96BE\u9053\u53C8\u6709\u4EC0\u4E48\u4E8B\u8981\u53D1\u751F\u4E86\u5417?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u4F55\u304B\u8D77\u3053\u308B\u524D\u5146\u306A\u306E\u304B\u306A\u2026"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 2
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 160
        y: 131
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 50, y: 0}
    canvasZoom: 1
  - id: 3
    fields:
    - title: Title
      value: ch0_msg_03
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: -1
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: None()
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 3
        originDialogueID: 0
        destinationConversationID: 3
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 160
        y: 30
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u54E5\u5E03\u6797!\u70BA\u4EC0\u9EBC\u9019\u88E1\u6703\u51FA\u73FE\u54E5\u5E03\u6797!"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Goblin! Why are there goblins here!?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u54E5\u5E03\u6797!\u70BA\u4EC0\u9EBC\u9019\u88E1\u6703\u51FA\u73FE\u54E5\u5E03\u6797!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u54E5\u5E03\u6797!\u4E3A\u4EC0\u4E48\u8FD9\u91CC\u4F1A\u51FA\u73B0\u54E5\u5E03\u6797!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u30B4\u30D6\u30EA\u30F3\uFF01\uFF1F\u306A\u3093\u3067\u3053\u3093\u306A\u3068\u3053\u308D\u306B\u30B4\u30D6\u30EA\u30F3\u304C\u3044\u308B\u3093\u3060\uFF01\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 3
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 160
        y: 79
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 1
  - id: 4
    fields:
    - title: Title
      value: ch0_msg_04
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: -1
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: None()
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 0
        destinationConversationID: 4
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      - originConversationID: 4
        originDialogueID: 0
        destinationConversationID: 4
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 508.2791
        y: 77.43502
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6211\u660E\u660E\u5DF2\u7D93\u6BB2\u6EC510\u516C\u91CC\u7BC4\u570D\u7684\u6240\u6709\u5DE2\u7A74\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I'm pretty sure I wiped out all the nearby goblin nests.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6211\u660E\u660E\u5DF2\u7D93\u6BB2\u6EC510\u516C\u91CC\u5167\u7684\u6240\u6709\u54E5\u5E03\u6797\u5DE2\u7A74\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u6211\u660E\u660E\u5DF2\u7ECF\u6B7C\u706D10\u516C\u91CC\u5185\u7684\u6240\u6709\u54E5\u5E03\u6797\u5DE2\u7A74\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "10\u30AD\u30ED\u570F\u5185\u306E\u30B4\u30D6\u30EA\u30F3\u306E\u5DE3\u306F\u5168\u6EC5\u3055\u305B\u305F\u306F\u305A\u306A\u306E\u306B\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 1
        destinationConversationID: 4
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 501.42844
        y: 183.15585
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u4E0D\u60F3\u4E86,\u9084\u662F\u5148\u56DE\u53BB\u78BA\u8A8D\u5ABD\u5ABD\u7684\u5B89\u5168\u5427\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Forget it. Gotta make sure Mom is okay.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u4E0D\u60F3\u4E86,\u9084\u662F\u5148\u56DE\u53BB\u78BA\u8A8D\u5ABD\u5ABD\u7684\u5B89\u5168\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u4E0D\u60F3\u4E86,\u8FD8\u662F\u5148\u56DE\u53BB\u786E\u8BA4\u5988\u5988\u7684\u5B89\u5168\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3068\u306B\u304B\u304F\u3001\u307E\u305A\u306F\u6BCD\u3055\u3093\u306E\u5B89\u5426\u3092\u78BA\u8A8D\u3057\u3088\u3046\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 501.42844
        y: 353.72073
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9019\u5E7E\u96BB\u54E5\u5E03\u6797\u7A76\u7ADF\u662F\u5F9E\u4F55\u800C\u4F86?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Where'd these goblins come from?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9019\u5E7E\u96BB\u54E5\u5E03\u6797\u7A76\u7ADF\u662F\u5F9E\u4F55\u800C\u4F86?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u8FD9\u51E0\u53EA\u54E5\u5E03\u6797\u7A76\u7ADF\u662F\u4ECE\u4F55\u800C\u6765?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3053\u306E\u30B4\u30D6\u30EA\u30F3\u305F\u3061\u306F\u4E00\u4F53\u3069\u3053\u304B\u3089\u6765\u305F\u3093\u3060\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 3
        destinationConversationID: 4
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 501.42844
        y: 269.7208
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9019\u662F......\u5E7B\u5F71\u72FC\uFF1F"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Is this... a Phantom Wolf?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9019\u662F......\u5E7B\u5F71\u72FC\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u8FD9\u662F\u2026\u2026\u5E7B\u5F71\u72FC\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3053\u308C\u306F\u2026\u2026\u5E7B\u5F71\u72FC\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 4
        destinationConversationID: 4
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 722.56476
        y: 183.14928
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5E7B\u5F71\u72FC\u600E\u9EBC\u6703\u5728\u9019\u500B\u6642\u9593\u9EDE\u51FA\u6C92\uFF1F"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Why would Phantom Wolves appear at this time?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5E7B\u5F71\u72FC\u600E\u9EBC\u6703\u5728\u9019\u500B\u6642\u9593\u9EDE\u51FA\u6C92\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5E7B\u5F71\u72FC\u600E\u4E48\u4F1A\u5728\u8FD9\u4E2A\u65F6\u95F4\u70B9\u51FA\u6CA1\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u306A\u305C\u5E7B\u5F71\u72FC\u304C\u3053\u306E\u6642\u9593\u306B\u51FA\u73FE\u3059\u308B\u3093\u3060\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 5
        destinationConversationID: 4
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 726.85034
        y: 254.57788
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5E7B\u5F71\u72FC\u662F\u591C\u884C\u6027\u7684\u9B54\u7269\uFF0C\u767D\u5929\u7167\u7406\u8AAA\u662F\u770B\u4E0D\u898B\u8E64\u8DE1\u7684\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Phantom Wolves are nocturnal monsters; you shouldn't see any sign
          of them during the day.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5E7B\u5F71\u72FC\u662F\u591C\u884C\u6027\u7684\u9B54\u7269\uFF0C\u767D\u5929\u7167\u7406\u8AAA\u662F\u770B\u4E0D\u898B\u8E64\u8DE1\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5E7B\u5F71\u72FC\u662F\u591C\u884C\u6027\u9B54\u7269\uFF0C\u767D\u5929\u7167\u7406\u8BF4\u662F\u770B\u4E0D\u89C1\u8E2A\u8FF9\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u5E7B\u5F71\u72FC\u306F\u591C\u884C\u6027\u306E\u9B54\u7269\u3060\u3002\u663C\u9593\u306F\u672C\u6765\u3001\u59FF\u304C\u898B\u3048\u306A\u3044\u306F\u305A\u306A\u306E\u306B\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 6
        destinationConversationID: 4
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 721.1361
        y: 348.86353
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5E7B\u5F71\u72FC\u4E0D\u6703\u7121\u7DE3\u7121\u6545\u96E2\u958B\u81EA\u5DF1\u7684\u68F2\u606F\u5730\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Phantom Wolves don't leave their habitats without a reason.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5E7B\u5F71\u72FC\u4E0D\u6703\u7121\u7DE3\u7121\u6545\u96E2\u958B\u81EA\u5DF1\u7684\u68F2\u606F\u5730\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5E7B\u5F71\u72FC\u4E0D\u4F1A\u65E0\u7F18\u65E0\u6545\u79BB\u5F00\u81EA\u5DF1\u7684\u6816\u606F\u5730\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u5E7B\u5F71\u72FC\u306F\u7406\u7531\u3082\u306A\u304F\u751F\u606F\u5730\u3092\u96E2\u308C\u308B\u3053\u3068\u306F\u306A\u3044\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 4
        originDialogueID: 7
        destinationConversationID: 4
        destinationDialogueID: 8
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 726.8504
        y: 417.43497
        width: 160
        height: 30
    - id: 8
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u770B\u4F86\u68EE\u6797\u6DF1\u8655\u4F3C\u4E4E\u662F\u51FA\u4E86\u4EC0\u9EBC\u4E8B......"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: It seems something has happened deep within the forest...
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u770B\u4F86\u68EE\u6797\u6DF1\u8655\u4F3C\u4E4E\u662F\u51FA\u4E86\u4EC0\u9EBC\u4E8B......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u770B\u6765\u68EE\u6797\u6DF1\u5904\u4F3C\u4E4E\u662F\u51FA\u4E86\u4EC0\u4E48\u4E8B\u2026\u2026"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3069\u3046\u3084\u3089\u68EE\u306E\u5965\u3067\u4F55\u304B\u304C\u8D77\u3053\u3063\u305F\u3088\u3046\u3060\u2026\u2026\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 4
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 735.4218
        y: 493.14926
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 97.888405}
    canvasZoom: 0.67000026
  - id: 5
    fields:
    - title: Title
      value: ch0_ML_CS_001_Conversation
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: None()
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 0
        destinationConversationID: 5
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      - originConversationID: 5
        originDialogueID: 0
        destinationConversationID: 5
        destinationDialogueID: 50
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1008.9697
        y: 155.77094
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u266B~\u266B~\u266B~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: "\u266B~\u266B~\u266B~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u266B~\u266B~\u266B~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u266B~\u266B~\u266B~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u266B~\u266B~\u266B~"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 1
        destinationConversationID: 5
        destinationDialogueID: 8
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1002.32104
        y: 219.927
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u771F\u662F\u7684~\u600E\u9EBC\u9084\u9019\u6A23\u6BDB\u6BDB\u8E81\u8E81\u7684\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: hmm......why are you still so naughty?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u771F\u662F\u7684~\u600E\u9EBC\u9084\u9019\u6A23\u6BDB\u6BDB\u8E81\u8E81\u7684......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u771F\u662F\u7684~\u600E\u4E48\u8FD8\u8FD9\u6837\u6BDB\u6BDB\u8E81\u8E81\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3082\u3046~\u3069\u3046\u3057\u3066\u305D\u3093\u306A\u306B\u614C\u3066\u3066\u3044\u308B\u306E\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 3
        destinationConversationID: 5
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1008.40784
        y: 327.27432
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9019\u4E0D\u662F\u60F3\u4F60\u4E86\u55CE\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Isn't this because I miss you.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9019\u4E0D\u662F\u60F3\u4F60\u4E86\u55CE\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u8FD9\u4E0D\u662F\u60F3\u4F60\u4E86\u5417\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3060\u3063\u3066\u3001\u541B\u306B\u4F1A\u3044\u305F\u304B\u3063\u305F\u304B\u3089\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 4
        destinationConversationID: 5
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1010.6736
        y: 379.99188
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5634\u5DF4\u751C\u4E5F\u6C92\u6709\u734E\u52F5\u5594~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Being sweet-talking won't earn you any rewards~
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5634\u5DF4\u751C\u4E5F\u6C92\u6709\u734E\u52F5\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u5634\u5DF4\u751C\u4E5F\u6CA1\u6709\u5956\u52B1\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u53E3\u304C\u7518\u304F\u3066\u3082\u3001\u4F55\u3082\u3042\u3052\u306A\u3044\u3088~"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 5
        destinationConversationID: 5
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1010.38306
        y: 441.15118
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u563F\u563F~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Hehe~
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u563F\u563F~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u563F\u563F~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3078\u3078~"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 6
        destinationConversationID: 5
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1012.6196
        y: 496.99994
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5C07\u98DF\u6750\u4EA4\u4ED8\u7D66\u5ABD\u5ABD\u4E4B\u5F8C......"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: After delivering the ingredients to stepmom......
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5C07\u98DF\u6750\u4EA4\u4ED8\u7D66\u5ABD\u5ABD\u4E4B\u5F8C......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u5C06\u98DF\u6750\u4EA4\u4ED8\u7ED9\u5988\u5988\u4E4B\u540E......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6BCD\u3055\u3093\u306B\u98DF\u6750\u3092\u6E21\u3057\u3066......"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 7
        destinationConversationID: 5
        destinationDialogueID: 25
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1018.9835
        y: 553.3971
        width: 160
        height: 30
    - id: 8
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6211\u56DE\u4F86\u5566!"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I'm back!
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6211\u56DE\u4F86\u5566!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u6211\u56DE\u6765\u5566!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u305F\u3060\u3044\u307E\uFF01"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 8
        destinationConversationID: 5
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1008.41614
        y: 274.0669
        width: 160
        height: 30
    - id: 25
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u8F9B\u82E6\u4E86,\u8DEF\u4E0A\u6709\u6C92\u6709\u9047\u5230\u5371\u96AA?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Thank you. I'm glad you're back safe. Did you run into any trouble
          along the way?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u8F9B\u82E6\u4E86,\u8DEF\u4E0A\u6709\u6C92\u6709\u9047\u5230\u5371\u96AA?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u8F9B\u82E6\u4E86,\u8DEF\u4E0A\u6709\u6CA1\u6709\u9047\u5230\u5371\u9669?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u304A\u75B2\u308C\u69D8\u3002\u9053\u4E2D\u3067\u4F55\u304B\u5371\u967A\u306A\u76EE\u306B\u906D\u3044\u307E\u3057\u305F\u304B\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 25
        destinationConversationID: 5
        destinationDialogueID: 26
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1020.6998
        y: 604.3078
        width: 160
        height: 30
    - id: 26
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6C92\u6709\u6C92\u6709!\u53EA\u662F\u53BB\u6536\u96C6\u98DF\u6750\u800C\u5DF2,\u600E\u9EBC\u6703\u6709\u5371\u96AA\u5462\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: It was just a simple trip to collect some ingredients. There was no
          danger involved.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6C92\u6709\u6C92\u6709!\u53EA\u662F\u53BB\u6536\u96C6\u98DF\u6750\u800C\u5DF2,\u600E\u9EBC\u6703\u6709\u5371\u96AA\u5462\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6CA1\u6709\u6CA1\u6709!\u53EA\u662F\u53BB\u6536\u96C6\u98DF\u6750\u800C\u5DF2,\u600E\u4E48\u4F1A\u6709\u5371\u9669\u5462\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3044\u3044\u3048\u3001\u3044\u3044\u3048\uFF01\u98DF\u6750\u3092\u96C6\u3081\u308B\u3060\u3051\u306A\u306E\u3067\u3001\u5371\u967A\u306A\u3053\u3068\u306F\u4F55\u3082\u3042\u308A\u307E\u305B\u3093\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 26
        destinationConversationID: 5
        destinationDialogueID: 27
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1020.4082
        y: 654.35034
        width: 160
        height: 30
    - id: 27
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u4E0D\u904E\u525B\u624D\u6211\u5728\u56DE\u4F86\u7684\u8DEF\u4E0A\u5012\u662F\u78B0\u5230\u4E86\u5E7E\u96BB\u54E5\u5E03\u6797\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: But on my way back, I actually came across a few goblins.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u4E0D\u904E\u525B\u624D\u6211\u5728\u56DE\u4F86\u7684\u8DEF\u4E0A\u5012\u662F\u78B0\u5230\u4E86\u5E7E\u96BB\u54E5\u5E03\u6797\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u4E0D\u8FC7\u65B9\u624D\u6211\u5728\u56DE\u6765\u7684\u8DEF\u4E0A\u5012\u662F\u78B0\u5230\u4E86\u51E0\u53EA\u54E5\u5E03\u6797\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3067\u3082\u3001\u3055\u3063\u304D\u5E30\u308B\u9014\u4E2D\u3067\u30B4\u30D6\u30EA\u30F3\u306B\u6570\u5339\u51FA\u4F1A\u3063\u305F\u3093\u3060\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 27
        destinationConversationID: 5
        destinationDialogueID: 28
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1020.9087
        y: 716.8841
        width: 160
        height: 30
    - id: 28
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6B38?\u6C92\u4E8B\u5427?\u8EAB\u9AD4\u6709\u6C92\u6709\u53D7\u50B7?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 'Oh no, are you alright? Did you get hurt? '
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6B38?\u6C92\u4E8B\u5427?\u8EAB\u9AD4\u6709\u6C92\u6709\u53D7\u50B7?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6B38?\u6CA1\u4E8B\u5427?\u8EAB\u4F53\u6709\u6CA1\u6709\u53D7\u4F24?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3048\uFF1F\u5927\u4E08\u592B\u3067\u3059\u304B\uFF1F\u3069\u3053\u304B\u602A\u6211\u3057\u307E\u3057\u305F\u304B\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 28
        destinationConversationID: 5
        destinationDialogueID: 29
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1024.3605
        y: 772.5184
        width: 160
        height: 30
    - id: 29
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6C92\u4E8B\u6C92\u4E8B,\u5340\u5340\u54E5\u5E03\u6797\u9084\u50B7\u4E0D\u5230\u6211\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Don't worry, I'm fine. Goblins are no match for me.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6C92\u4E8B\u6C92\u4E8B,\u5340\u5340\u54E5\u5E03\u6797\u9084\u50B7\u4E0D\u5230\u6211\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6CA1\u4E8B\u6CA1\u4E8B,\u533A\u533A\u54E5\u5E03\u6797\u8FD8\u4F24\u4E0D\u5230\u6211\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u5927\u4E08\u592B\u3001\u5927\u4E08\u592B\u3002 \u30B4\u30D6\u30EA\u30F3\u306A\u3093\u3066\u79C1\u306B\u306F\u50B7\u3064\u3051\u3089\u308C\u307E\u305B\u3093\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 29
        destinationConversationID: 5
        destinationDialogueID: 30
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1026.0981
        y: 821.92535
        width: 160
        height: 30
    - id: 30
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u90A3\u5C31\u597D,\u5E73\u6642\u51FA\u53BB\u9084\u662F\u8981\u8B39\u614E\u4E00\u9EDE,\u8981\u662F\u51FA\u610F\u5916\u53EF\u5C31\u5F97\u4E0D\u511F\u5931\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: That's good to hear. But you should still be careful when you go out.
          It wouldn't be worth it if something happened.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u90A3\u5C31\u597D,\u5E73\u6642\u51FA\u53BB\u9084\u662F\u8981\u8B39\u614E\u4E00\u9EDE,\u8981\u662F\u51FA\u610F\u5916\u53EF\u5C31\u5F97\u4E0D\u511F\u5931\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u90A3\u5C31\u597D,\u5E73\u65F6\u51FA\u53BB\u8FD8\u662F\u8981\u8C28\u614E\u4E00\u70B9,\u8981\u662F\u51FA\u610F\u5916\u53EF\u5C31\u5F97\u4E0D\u507F\u5931\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u305D\u308C\u306F\u3088\u304B\u3063\u305F\u3002\u3067\u3082\u3001\u666E\u6BB5\u304B\u3089\u6C17\u3092\u4ED8\u3051\u3066\u304F\u3060\u3055\u3044\u306D\u3002\u4F55\u304B\u3042\u3063\u305F\u3089\u5927\u5909\u3067\u3059\u304B\u3089\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 30
        destinationConversationID: 5
        destinationDialogueID: 31
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1023.273
        y: 880.8967
        width: 160
        height: 30
    - id: 31
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u77E5\u9053\u4E86,\u4E0D\u904E\u6211\u9084\u662F\u60F3\u4E0D\u901A\u70BA\u4F55\u6703\u6709\u54E5\u5E03\u6797,\u65E9\u4E0A\u6211\u51FA\u53BB\u6642\u4E00\u5207\u6B63\u5E38,\u56DE\u7A0B\u8DEF\u4E0A\u537B\u591A\u51FA\u4E86\u54E5\u5E03\u6797,\u5C31\u50CF\u662F\u6191\u7A7A\u51FA\u73FE\u7684\u4E00\u6A23\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I get it, but it's weird. Like, one minute everything was cool, and
          the next, there were goblins.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u77E5\u9053\u4E86,\u4E0D\u904E\u6211\u9084\u662F\u60F3\u4E0D\u901A\u70BA\u4F55\u6703\u6709\u54E5\u5E03\u6797,\u65E9\u4E0A\u6211\u51FA\u53BB\u6642\u4E00\u5207\u6B63\u5E38,\u56DE\u7A0B\u8DEF\u4E0A\u537B\u591A\u51FA\u4E86\u54E5\u5E03\u6797,\u5C31\u50CF\u662F\u6191\u7A7A\u51FA\u73FE\u7684\u4E00\u6A23\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u77E5\u9053\u4E86,\u4E0D\u8FC7\u6211\u8FD8\u662F\u60F3\u4E0D\u901A\u4E3A\u4F55\u4F1A\u6709\u54E5\u5E03\u6797,\u65E9\u4E0A\u6211\u51FA\u53BB\u65F6\u4E00\u5207\u6B63\u5E38,\u56DE\u7A0B\u8DEF\u4E0A\u5374\u591A\u51FA\u4E86\u54E5\u5E03\u6797,\u5C31\u50CF\u662F\u51ED\u7A7A\u51FA\u73B0\u7684\u4E00\u6837\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u306F\u3044\u306F\u3044\u3002\u3067\u3082\u3001\u3069\u3046\u3057\u3066\u30B4\u30D6\u30EA\u30F3\u304C\u3044\u308B\u3093\u3060\u308D\u3046\u3002\u671D\u306F\u4F55\u3082\u3044\u306A\u304B\u3063\u305F\u306E\u306B\u3001\u5E30\u308A\u9053\u306B\u7A81\u7136\u73FE\u308C\u305F\u306E\u306F\u4E0D\u601D\u8B70\u3060\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 31
        destinationConversationID: 5
        destinationDialogueID: 32
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1026.0142
        y: 937.8563
        width: 160
        height: 30
    - id: 32
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u800C\u4E14\u90A3\u5E7E\u96BB\u54E5\u5E03\u6797\u7684\u72C0\u614B\u760B\u760B\u7672\u7672\u7684,\u770B\u8D77\u4F86\u4E0D\u50CF\u662F\u6B63\u5E38\u7684\u6A23\u5B50\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: And those goblins were acting really strange. They didn't seem normal
          at all.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u800C\u4E14\u90A3\u5E7E\u96BB\u54E5\u5E03\u6797\u7684\u72C0\u614B\u760B\u760B\u7672\u7672\u7684,\u770B\u8D77\u4F86\u4E0D\u50CF\u662F\u6B63\u5E38\u7684\u6A23\u5B50\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u800C\u4E14\u90A3\u51E0\u53EA\u54E5\u5E03\u6797\u7684\u72B6\u6001\u75AF\u75AF\u766B\u766B\u7684,\u770B\u8D77\u6765\u4E0D\u50CF\u662F\u6B63\u5E38\u7684\u6837\u5B50\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3057\u304B\u3082\u3001\u3042\u306E\u30B4\u30D6\u30EA\u30F3\u305F\u3061\u306F\u3001\u69D8\u5B50\u304C\u304A\u304B\u3057\u304B\u3063\u305F\u3002\u307E\u308B\u3067\u72C2\u3063\u3066\u3044\u308B\u3088\u3046\u3060\u3063\u305F\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 32
        destinationConversationID: 5
        destinationDialogueID: 33
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1209.643
        y: 222.64023
        width: 160
        height: 30
    - id: 33
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6211\u60F3\u9644\u8FD1\u53EF\u80FD\u6709\u7570\u9580\u6253\u958B\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: There might be a Weird Gate opened nearby.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6211\u60F3\u9644\u8FD1\u53EF\u80FD\u6709\u7570\u9580\u6253\u958B\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6211\u731C\u9644\u8FD1\u53EF\u80FD\u6709\u5F02\u95E8\u6253\u5F00\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3082\u3057\u304B\u3057\u305F\u3089\u3001\u8FD1\u304F\u306B\u7570\u9580\u304C\u958B\u3044\u305F\u306E\u304B\u3082\u3057\u308C\u306A\u3044\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 33
        destinationConversationID: 5
        destinationDialogueID: 34
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1210.8573
        y: 271.9729
        width: 160
        height: 30
    - id: 34
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u7570\u9580?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Weird Gate?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u7570\u9580?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5F02\u95E8?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u7570\u9580?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 34
        destinationConversationID: 5
        destinationDialogueID: 35
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1212.0072
        y: 324.2822
        width: 160
        height: 30
    - id: 35
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5169\u5343\u5E74\u524D\u4E16\u754C\u5404\u5730\u958B\u59CB\u51FA\u73FE\u4E86\u5404\u7A2E\u5927\u5C0F\u4E0D\u4E00\u7684\u7A7A\u9593\u88C2\u7E2B,\u6709\u8A31\u591A\u5147\u6B98\u7684\u9B54\u7269\u5F9E\u88E1\u9762\u6E67\u51FA\u4F86,\u4E26\u9020\u6210\u4E86\u7121\u6578\u4EBA\u7684\u50B7\u4EA1\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 'Two thousand years ago, spatial rifts of various sizes began appearing
          all over the world. Countless ferocious monsters emerged from these rifts,
          causing massive casualties. '
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5169\u5343\u5E74\u524D\u4E16\u754C\u5404\u5730\u958B\u59CB\u51FA\u73FE\u4E86\u5404\u7A2E\u5927\u5C0F\u4E0D\u4E00\u7684\u7A7A\u9593\u88C2\u7E2B,\u6709\u8A31\u591A\u5147\u6B98\u7684\u9B54\u7269\u5F9E\u88E1\u9762\u6E67\u51FA\u4F86,\u4E26\u9020\u6210\u4E86\u7121\u6578\u4EBA\u7684\u50B7\u4EA1\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u4E24\u5343\u5E74\u524D\u4E16\u754C\u5404\u5730\u5F00\u59CB\u51FA\u73B0\u4E86\u5404\u79CD\u5927\u5C0F\u4E0D\u4E00\u7684\u7A7A\u95F4\u88C2\u7F1D,\u6709\u8BB8\u591A\u51F6\u6B8B\u7684\u9B54\u7269\u4ECE\u91CC\u9762\u6D8C\u51FA\u6765,\u5E76\u9020\u6210\u4E86\u65E0\u6570\u4EBA\u7684\u4F24\u4EA1\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u4E8C\u5343\u5E74\u524D\u304B\u3089\u3001\u4E16\u754C\u4E2D\u3067\u5927\u5C0F\u3055\u307E\u3056\u307E\u306A\u7A7A\u9593\u306E\u88C2\u3051\u76EE\u304C\u73FE\u308C\u59CB\u3081\u3001\u305D\u3053\u304B\u3089\u51F6\u66B4\u306A\u9B54\u7269\u304C\u73FE\u308C\u3066\u591A\u304F\u306E\u4EBA\u3005\u3092\u50B7\u3064\u3051\u305F\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 35
        destinationConversationID: 5
        destinationDialogueID: 46
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1211.7682
        y: 378.65747
        width: 160
        height: 30
    - id: 36
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5594\u5594,\u597D\u50CF\u662F\u6709\u9019\u9EBC\u4E00\u500B\u6771\u897F\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Oh, I think there is something like that.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5594\u5594,\u597D\u50CF\u662F\u6709\u9019\u9EBC\u4E00\u500B\u6771\u897F\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5594\u5594,\u597D\u50CF\u662F\u6709\u8FD9\u4E48\u4E00\u4E2A\u4E1C\u897F\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3042\u3042\u3001\u305D\u3093\u306A\u3082\u306E\u304C\u3042\u3063\u305F\u306D\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 36
        destinationConversationID: 5
        destinationDialogueID: 37
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1210.4281
        y: 496.83105
        width: 160
        height: 30
    - id: 37
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u4F46\u662F\u6211\u5011\u5BB6\u9644\u8FD1\u600E\u9EBC\u6703\u7A81\u7136\u51FA\u73FE\u7570\u9580?\u9019\u5E7E\u5E74\u660E\u660E\u90FD\u5F88\u5B89\u7A69\u7684\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: But how could Weird Gate suddenly appear near my house? Things have
          been so peaceful these past few years.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u4F46\u662F\u6211\u5011\u5BB6\u9644\u8FD1\u600E\u9EBC\u6703\u7A81\u7136\u51FA\u73FE\u7570\u9580?\u9019\u5E7E\u5E74\u660E\u660E\u90FD\u5F88\u5B89\u7A69\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u4F46\u662F\u6211\u4EEC\u5BB6\u9644\u8FD1\u600E\u4E48\u4F1A\u7A81\u7136\u51FA\u73B0\u5F02\u95E8?\u8FD9\u51E0\u5E74\u660E\u660E\u90FD\u5F88\u5B89\u7A33\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3067\u3082\u3001\u3069\u3046\u3057\u3066\u3046\u3061\u306E\u8FD1\u304F\u306B\u7A81\u7136\u7570\u9580\u304C\u73FE\u308C\u308B\u3093\u3060\uFF1F\u3053\u3053\u6570\u5E74\u306F\u4F55\u3082\u306A\u304B\u3063\u305F\u306E\u306B\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 37
        destinationConversationID: 5
        destinationDialogueID: 38
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1209.8252
        y: 552.1326
        width: 160
        height: 30
    - id: 38
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u78BA\u5BE6\u5F88\u5947\u602A,\u4E0D\u904E\u7121\u8AD6\u5982\u4F55\u6211\u5011\u90FD\u8981\u5148\u505A\u597D\u6E96\u5099\u624D\u884C\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: That's very strange, but we need to prepare for the worst no matter
          what.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u78BA\u5BE6\u5F88\u5947\u602A,\u4E0D\u904E\u7121\u8AD6\u5982\u4F55\u6211\u5011\u90FD\u8981\u5148\u505A\u597D\u6E96\u5099\u624D\u884C\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u786E\u5B9E\u5F88\u5947\u602A,\u4E0D\u8FC7\u65E0\u8BBA\u5982\u4F55\u6211\u4EEC\u90FD\u8981\u5148\u505A\u597D\u51C6\u5907\u624D\u884C\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u78BA\u304B\u306B\u5947\u5999\u306A\u8A71\u3060\u304C\u3001\u3044\u305A\u308C\u306B\u305B\u3088\u6E96\u5099\u3060\u3051\u306F\u3057\u3066\u304A\u3053\u3046\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 38
        destinationConversationID: 5
        destinationDialogueID: 39
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1210.4427
        y: 602.3807
        width: 160
        height: 30
    - id: 39
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u597D!\u90FD\u807D\u5ABD\u5ABD\u7684!"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: '"Okay, stepmom! Of course, I''ll follow your advice."'
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u597D!\u90FD\u807D\u5ABD\u5ABD\u7684!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u597D!\u90FD\u542C\u5988\u5988\u7684!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u306F\u3044\u3001\u6BCD\u3055\u3093\u306E\u8A00\u3046\u3053\u3068\u3001\u306A\u3093\u3067\u3082\u805E\u304D\u307E\uFF5E\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 39
        destinationConversationID: 5
        destinationDialogueID: 40
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1209.282
        y: 655.02606
        width: 160
        height: 30
    - id: 40
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u597D\u5566~\u4E0D\u8AC7\u9019\u4E9B\u4E86,\u4E5F\u8A72\u716E\u5348\u9910\u4E86,\u4E2D\u5348\u5403\u99AC\u9234\u85AF\u71C9\u8089\u600E\u9EBC\u6A23?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Alright, let's drop it. It's time to cook lunch anyway. How about
          some potato stew?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u597D\u5566~\u4E0D\u8AC7\u9019\u4E9B\u4E86,\u4E5F\u8A72\u716E\u5348\u9910\u4E86,\u4E2D\u5348\u5403\u99AC\u9234\u85AF\u71C9\u8089\u600E\u9EBC\u6A23?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u597D\u5566~\u4E0D\u8C08\u8FD9\u4E9B\u4E86,\u4E5F\u8BE5\u716E\u5348\u9910\u4E86,\u4E2D\u5348\u5403\u9A6C\u94C3\u85AF\u7096\u8089\u600E\u4E48\u6837?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u305D\u308D\u305D\u308D\u304A\u663C\u3054\u98EF\u306E\u6642\u9593\u3060\u3057\u3001\u8089\u3058\u3083\u304C\u306F\u3069\u3046\u304B\u306A\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 40
        destinationConversationID: 5
        destinationDialogueID: 41
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1209.5057
        y: 714.458
        width: 160
        height: 30
    - id: 41
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5ABD\u5ABD\u716E\u4EC0\u9EBC\u90FD\u597D\u5403!"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Everything my stepmom cooks is delicious!
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5ABD\u5ABD\u716E\u4EC0\u9EBC\u90FD\u597D\u5403!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5988\u5988\u716E\u4EC0\u4E48\u90FD\u597D\u5403!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6BCD\u3055\u3093\u304C\u4F5C\u308B\u6599\u7406\u306F\u5168\u90E8\u5927\u597D\u304D\uFF01"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 41
        destinationConversationID: 5
        destinationDialogueID: 42
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1211.2756
        y: 772.503
        width: 160
        height: 30
    - id: 42
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5475\u5475~\u5C11\u8CA7\u5634\u4E86,\u7D66\u5ABD\u5ABD\u4E00\u9EDE\u6642\u9593\u6E96\u5099\u5348\u9910,\u9019\u6BB5\u6642\u9593\u4F60\u5148\u53BB\u4F0A\u52E2\u5BB6\u7684\u5893\u5712\u4E0A\u500B\u9999\u5427\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Please cut the chatter and give me some time to prepare lunch. In
          the meantime, you can go to the grave and offer incense.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5475\u5475~\u5C11\u8CA7\u5634\u4E86,\u7D66\u5ABD\u5ABD\u4E00\u9EDE\u6642\u9593\u6E96\u5099\u5348\u9910,\u9019\u6BB5\u6642\u9593\u4F60\u5148\u53BB\u4F0A\u52E2\u5BB6\u7684\u5893\u5712\u4E0A\u500B\u9999\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5475\u5475~\u5C11\u8D2B\u5634\u4E86,\u7ED9\u5988\u5988\u4E00\u70B9\u65F6\u95F4\u51C6\u5907\u5348\u9910,\u8FD9\u6BB5\u65F6\u95F4\u4F60\u5148\u53BB\u4F0A\u52BF\u5BB6\u7684\u5893\u56ED\u4E0A\u4E2A\u9999\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u305D\u3093\u306A\u3053\u3068\u8A00\u308F\u305A\u306B\u3001\u304A\u6BCD\u3055\u3093\u306B\u3054\u98EF\u306E\u6E96\u5099\u3092\u3055\u305B\u3066\u3042\u3052\u3088\u3046\u3002\u3053\u306E\u9593\u306B\u4F0A\u52E2\u5BB6\u306E\u5893\u53C2\u308A\u306B\u884C\u3063\u3066\u304A\u3044\u3067\u3002\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 42
        destinationConversationID: 5
        destinationDialogueID: 43
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1216.0344
        y: 822.7911
        width: 160
        height: 30
    - id: 43
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6536\u5230!"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Got it.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6536\u5230!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6536\u5230!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u5206\u304B\u308A\u307E\u3057\u305F\uFF01"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 43
        destinationConversationID: 5
        destinationDialogueID: 44
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1215.584
        y: 882.45605
        width: 160
        height: 30
    - id: 44
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9999\u6211\u653E\u5728\u623F\u5B50\u88E1\u5DE6\u908A\u7684\u6AC3\u5B50\u4E0A,\u4E0D\u8981\u5FD8\u8A18\u5594~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I've put the incense on the left-side cabinet in the house. Don't
          forget it.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9999\u6211\u653E\u5728\u623F\u5B50\u88E1\u5DE6\u908A\u7684\u6AC3\u5B50\u4E0A,\u4E0D\u8981\u5FD8\u8A18\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u9999\u6211\u653E\u5728\u623F\u5B50\u91CC\u5DE6\u8FB9\u7684\u67DC\u5B50\u4E0A,\u4E0D\u8981\u5FD8\u8BB0\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u304A\u9999\u306F\u3001\u5BB6\u306E\u5DE6\u5074\u306E\u6238\u68DA\u306B\u7F6E\u304D\u307E\u3057\u305F\u3002\u5FD8\u308C\u306A\u3044\u3067\u306D\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 44
        destinationConversationID: 5
        destinationDialogueID: 45
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1214.1158
        y: 938.9064
        width: 160
        height: 30
    - id: 45
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u597D~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I see.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u597D~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u597D~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u306F\u3044~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1215.6241
        y: 996.4108
        width: 160
        height: 30
    - id: 46
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5728\u90A3\u4E4B\u5F8C\u90A3\u4E9B\u4E0D\u77E5\u4F55\u6642\u6703\u51FA\u73FE\u7684\u7A7A\u9593\u7E2B\u9699\u4FBF\u88AB\u7D71\u7A31\u70BA\u300C\u7570\u9580\u300D\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: From then on, the unpredictable spatial rifts were uniformly referred
          to as "Weird Gate."
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5728\u90A3\u4E4B\u5F8C\u90A3\u4E9B\u4E0D\u77E5\u4F55\u6642\u6703\u51FA\u73FE\u7684\u7A7A\u9593\u7E2B\u9699\u4FBF\u88AB\u7D71\u7A31\u70BA\u300C\u7570\u9580\u300D\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5728\u90A3\u4E4B\u540E\u90A3\u4E9B\u4E0D\u77E5\u4F55\u65F6\u4F1A\u51FA\u73B0\u7684\u7A7A\u95F4\u7F1D\u9699\u4FBF\u88AB\u7EDF\u79F0\u4E3A\u300C\u5F02\u95E8\u300D\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u305D\u308C\u304B\u3089\u3001\u3044\u3064\u73FE\u308C\u308B\u304B\u308F\u304B\u3089\u306A\u3044\u7A7A\u9593\u306E\u88C2\u3051\u76EE\u306F\u300C\u7570\u9580\u300D\u3068\u547C\u3070\u308C\u308B\u3088\u3046\u306B\u306A\u3063\u305F\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 46
        destinationConversationID: 5
        destinationDialogueID: 36
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1211.7693
        y: 436.82315
        width: 160
        height: 30
    - id: 48
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5ABD\u5ABD\u5FEB\u8D70\uFF01"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Mom run!
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5ABD\u5ABD\u5FEB\u8D70\uFF01"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u5988\u5988\u5FEB\u8D70\uFF01"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6BCD\u3055\u3093!\u9003\u3052\u308D\uFF01"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 48
        destinationConversationID: 5
        destinationDialogueID: 49
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1889.8579
        y: 283.6148
        width: 160
        height: 30
    - id: 49
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u901F\u6230\u901F\u6C7A\uFF01"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Let's get it over with!
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u901F\u6230\u901F\u6C7A\u5427!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: zh-cn
        value: "\u901F\u6218\u901F\u51B3\u5427!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u901F\u6226\u901F\u6C7A\u3057\u3088\u3046\uFF01"
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1892.3892
        y: 341.1464
        width: 160
        height: 30
    - id: 50
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 5
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 5
        originDialogueID: 50
        destinationConversationID: 5
        destinationDialogueID: 48
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 1890.9287
        y: 214.03357
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 565.1339, y: 662.97107}
    canvasZoom: 0.63999957
  - id: 7
    fields:
    - title: Title
      value: ch0_ML_Default_001
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Parenthetical
        value: 
        type: 0
        typeString: 
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: 
      - title: EventGuid
        value: 
        type: 0
        typeString: 
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: 
      conversationID: 7
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 7
        originDialogueID: 0
        destinationConversationID: 7
        destinationDialogueID: 36
        isConnector: 0
        priority: 2
      - originConversationID: 7
        originDialogueID: 0
        destinationConversationID: 7
        destinationDialogueID: 37
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 159
        y: 29
        width: 160
        height: 30
    - id: 36
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 0
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 0
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u53F2\u840A\u59C6\u90FD\u6E05\u7406\u4E7E\u6DE8\u4E86!\u63A5\u4E0B\u4F86\u53BB\u67E5\u770B\u9A45\u9B54\u53F0\u7684\u72C0\u6CC1\u5427!"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The slimes are all cleared! Let's go check on the exorcism platform
          next!
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u53F2\u840A\u59C6\u90FD\u6E05\u7406\u4E7E\u6DE8\u4E86!\u63A5\u4E0B\u4F86\u53BB\u67E5\u770B\u9A45\u9B54\u53F0\u7684\u72C0\u6CC1\u5427!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u53F2\u83B1\u59C6\u90FD\u6E05\u7406\u5E72\u51C0\u4E86!\u63A5\u4E0B\u6765\u53BB\u67E5\u770B\u9A71\u9B54\u53F0\u7684\u72B6\u51B5\u5427!"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u30B9\u30E9\u30A4\u30E0\u306F\u5168\u90E8\u7247\u4ED8\u3051\u305F\uFF01\u6B21\u306F\u9A45\u9B54\u53F0\u306E\u72C0\u6CC1\u3092\u898B\u306B\u884C\u3053\u3046\uFF01"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 7
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 64.38132
        y: 133.50024
        width: 160
        height: 30
    - id: 37
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 0
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 0
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9A45\u9B54\u53F0\u770B\u8D77\u4F86\u6C92\u51FA\u4EC0\u9EBC\u554F\u984C,\u53EF\u4EE5\u653E\u5FC3\u53BB\u5893\u5712\u4E0A\u9999\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The exorcism platform seems to be fine, so we can go to the cemetery
          to offer incense without worry.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9A45\u9B54\u53F0\u770B\u8D77\u4F86\u6C92\u51FA\u4EC0\u9EBC\u554F\u984C,\u53EF\u4EE5\u653E\u5FC3\u53BB\u5893\u5712\u4E0A\u9999\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u9A71\u9B54\u53F0\u770B\u8D77\u6765\u6CA1\u51FA\u4EC0\u4E48\u95EE\u9898,\u53EF\u4EE5\u653E\u5FC3\u53BB\u5893\u56ED\u4E0A\u9999\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u9A45\u9B54\u53F0\u306F\u7279\u306B\u554F\u984C\u306A\u3055\u305D\u3046\u3060\u3002\u5B89\u5FC3\u3057\u3066\u5893\u53C3\u308A\u306B\u884C\u3051\u308B\u306D\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 7
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 283.6486
        y: 136.3501
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 1.1199994
  - id: 8
    fields:
    - title: Title
      value: Ch0_ML_YumikoIse_NPC
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: None()
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 0
        destinationConversationID: 8
        destinationDialogueID: 22
        isConnector: 0
        priority: 2
      - originConversationID: 8
        originDialogueID: 0
        destinationConversationID: 8
        destinationDialogueID: 36
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 161.6
        y: 28.80951
        width: 160
        height: 30
    - id: 22
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9999\u653E\u5728\u623F\u5B50\u88E1\u5DE6\u908A\u7684\u6AC3\u5B50\u4E0A,\u4E0D\u8981\u5FD8\u8A18\u5594~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I've put the incense on the left-side cabinet in the house. Don't
          forget it.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9999\u653E\u5728\u623F\u5B50\u88E1\u5DE6\u908A\u7684\u6AC3\u5B50\u4E0A,\u4E0D\u8981\u5FD8\u8A18\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u9999\u653E\u5728\u623F\u5B50\u91CC\u5DE6\u8FB9\u7684\u67DC\u5B50\u4E0A,\u4E0D\u8981\u5FD8\u8BB0\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u304A\u9999\u306F\u3001\u5BB6\u306E\u5DE6\u5074\u306E\u6238\u68DA\u306B\u7F6E\u304D\u307E\u3057\u305F\u3002\u5FD8\u308C\u306A\u3044\u3067\u306D\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: "CurrentQuestEntryState(\"\u4E0A\u9999\u6E96\u5099\", 3)
        == \"unassigned\""
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 31.933594
        y: 122.15773
        width: 160
        height: 30
    - id: 36
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6771\u897F\u90FD\u6E96\u5099\u597D\u4E86\u55CE?\u4E0D\u8981\u5FD8\u8A18\u5E36\u5594~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 'CinemachinePriority(Ch0_ML_2_Cam);

          {{default}}'
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Are you all packed? Don't forget anything~
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6771\u897F\u90FD\u6E96\u5099\u597D\u4E86\u55CE?\u4E0D\u8981\u5FD8\u8A18\u5E36\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u4E1C\u897F\u90FD\u51C6\u5907\u597D\u4E86\u5417?\u4E0D\u8981\u5FD8\u8BB0\u5E26\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6E96\u5099\u306F\u5927\u4E08\u592B\uFF1F\u5FD8\u308C\u7269\u3057\u306A\u3044\u3088\u3046\u306B\u306D\uFF5E"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 36
        destinationConversationID: 8
        destinationDialogueID: 37
        isConnector: 0
        priority: 2
      conditionsString: "CurrentQuestEntryState(\"\u4E0A\u9999\u6E96\u5099\", 3)
        == \"returnToNPC\" or (CurrentQuestState(\"\u4E0A\u9999\u6E96\u5099\") ==
        \"success\")"
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 278.4658
        y: 125.41121
        width: 160
        height: 30
    - id: 37
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u653E\u5FC3\u5427,\u6771\u897F\u90FD\u6E05\u9EDE\u904E\u4E00\u6B21,\u6C92\u6709\u5FD8\u8A18\u5E36\u7684\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Don't worry, I've checked everything once, I didn't forget anything.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u653E\u5FC3\u5427,\u6771\u897F\u90FD\u6E05\u9EDE\u904E\u4E00\u6B21,\u6C92\u6709\u5FD8\u8A18\u5E36\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u653E\u5FC3\u5427,\u4E1C\u897F\u90FD\u6E05\u70B9\u8FC7\u4E00\u6B21,\u6CA1\u6709\u5FD8\u8BB0\u5E26\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u5B89\u5FC3\u3057\u3066\u304F\u3060\u3055\u3044\u3001\u6301\u3061\u7269\u306F\u4E00\u5EA6\u5168\u90E8\u78BA\u8A8D\u3057\u305F\u306E\u3067\u3001\u5FD8\u308C\u7269\u306F\u3042\u308A\u307E\u305B\u3093\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 37
        destinationConversationID: 8
        destinationDialogueID: 38
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 281.8817
        y: 201.54753
        width: 160
        height: 30
    - id: 38
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u90A3\u5C31\u597D,\u5ABD\u5ABD\u7B49\u7B49\u9084\u6709\u9EDE\u4E8B\u8981\u505A,\u6240\u4EE5\u53EA\u80FD\u5148\u62DC\u8A17\u4F60\u81EA\u5DF1\u4E00\u500B\u4EBA\u53BB\u5893\u5712\u4E0A\u9999\u4E86,\u4E0A\u9999\u7684\u6B65\u9A5F\u90FD\u8A18\u5F97\u5427\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: That's good. Stepmom has some things to do later, so I'll have to
          ask you to go to the cemetery to offer incense by yourself. You remember
          the steps for offering incense, right?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u90A3\u5C31\u597D,\u5ABD\u5ABD\u7B49\u7B49\u9084\u6709\u9EDE\u4E8B\u8981\u505A,\u6240\u4EE5\u53EA\u80FD\u5148\u62DC\u8A17\u4F60\u81EA\u5DF1\u4E00\u500B\u4EBA\u53BB\u5893\u5712\u4E0A\u9999\u4E86,\u4E0A\u9999\u7684\u6B65\u9A5F\u90FD\u8A18\u5F97\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u90A3\u5C31\u597D,\u5988\u5988\u7B49\u7B49\u8FD8\u6709\u70B9\u4E8B\u8981\u505A,\u6240\u4EE5\u53EA\u80FD\u5148\u62DC\u6258\u4F60\u81EA\u5DF1\u4E00\u4E2A\u4EBA\u53BB\u5893\u56ED\u4E0A\u9999\u4E86,\u4E0A\u9999\u7684\u6B65\u9AA4\u90FD\u8BB0\u5F97\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3088\u304B\u3063\u305F\u308F\u3002\u304A\u6BCD\u3055\u3093\u3001\u5F8C\u3067\u5C11\u3057\u7528\u4E8B\u304C\u3042\u308B\u304B\u3089\u3001\u5148\u306B\u304A\u5893\u53C2\u308A\u306B\u884C\u3063\u3066\u3082\u3089\u3048\u308B\u304B\u3057\u3089\uFF1F\u304A\u7DDA\u9999\u3092\u3042\u3052\u308B\u624B\u9806\u306F\u899A\u3048\u3066\u308B\u308F\u3088\u306D\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 38
        destinationConversationID: 8
        destinationDialogueID: 39
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 284.172
        y: 271.28168
        width: 160
        height: 30
    - id: 39
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u8A18\u5F97\u8A18\u5F97,\u6BCF\u5468\u90FD\u8981\u53BB\u5893\u5712\u4E0A\u9999\u4E00\u6B21,\u6211\u600E\u9EBC\u6703\u5FD8\u5462?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Of course I remember, we go to the cemetery to offer incense every
          week, how could I forget?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u8A18\u5F97\u8A18\u5F97,\u6BCF\u5468\u90FD\u8981\u53BB\u5893\u5712\u4E0A\u9999\u4E00\u6B21,\u6211\u600E\u9EBC\u6703\u5FD8\u5462?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u8BB0\u5F97\u8BB0\u5F97,\u6BCF\u5468\u90FD\u8981\u53BB\u5893\u56ED\u4E0A\u9999\u4E00\u6B21,\u6211\u600E\u4E48\u4F1A\u5FD8\u5462?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u899A\u3048\u3066\u308B\u3001\u6BCE\u9031\u304A\u5893\u53C2\u308A\u306B\u884C\u304F\u3093\u3060\u304B\u3089\u3001\u5FD8\u308C\u308B\u308F\u3051\u306A\u3044\u3067\u3057\u3087\u3046\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 39
        destinationConversationID: 8
        destinationDialogueID: 40
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 287.70306
        y: 343.68695
        width: 160
        height: 30
    - id: 40
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u90A3\u5C31\u62DC\u8A17\u4F60\u56C9,\u7B49\u4F60\u56DE\u4F86,\u5348\u9910\u61C9\u8A72\u4E5F\u5DEE\u4E0D\u591A\u505A\u5B8C\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Then I'll leave it to you. By the time you get back, lunch should
          be almost ready.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u90A3\u5C31\u62DC\u8A17\u4F60\u56C9,\u7B49\u4F60\u56DE\u4F86,\u5348\u9910\u61C9\u8A72\u4E5F\u5DEE\u4E0D\u591A\u505A\u5B8C\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u90A3\u5C31\u62DC\u6258\u4F60\u5570,\u7B49\u4F60\u56DE\u6765,\u5348\u9910\u5E94\u8BE5\u4E5F\u5DEE\u4E0D\u591A\u505A\u5B8C\u4E86\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3058\u3083\u3042\u3001\u304A\u9858\u3044\u306D\u3002\u3042\u306A\u305F\u304C\u5E30\u3063\u3066\u304F\u308B\u9803\u306B\u306F\u3001\u304A\u663C\u3054\u98EF\u3082\u5927\u4F53\u3067\u304D\u3066\u308B\u3068\u601D\u3046\u308F\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 40
        destinationConversationID: 8
        destinationDialogueID: 41
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 281.45398
        y: 415.99695
        width: 160
        height: 30
    - id: 41
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u90A3\u771F\u662F\u592A\u597D\u4E86,\u671F\u5F85\u5ABD\u5ABD\u505A\u7684\u7F8E\u5473\u5348\u9910\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: That's wonderful, I'm looking forward to Stepmom's delicious lunch.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u90A3\u771F\u662F\u592A\u597D\u4E86,\u671F\u5F85\u5ABD\u5ABD\u505A\u7684\u7F8E\u5473\u5348\u9910\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u90A3\u771F\u662F\u592A\u597D\u4E86,\u671F\u5F85\u5988\u5988\u505A\u7684\u7F8E\u5473\u5348\u9910\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u305D\u308C\u306F\u3088\u304B\u3063\u305F\u3002\u304A\u6BCD\u3055\u3093\u306E\u7F8E\u5473\u3057\u3044\u304A\u663C\u3054\u98EF\u3001\u697D\u3057\u307F\u306B\u3057\u3066\u308B\u306D\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 41
        destinationConversationID: 8
        destinationDialogueID: 42
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 283.4706
        y: 486.87872
        width: 160
        height: 30
    - id: 42
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5C0D\u4E86,\u5916\u9762\u597D\u50CF\u6709\u5E7E\u96BB\u53F2\u840A\u59C6\u5728\u9A45\u9B54\u53F0\u9644\u8FD1\u9592\u6643,\u70BA\u4E86\u907F\u514D\u9A45\u9B54\u53F0\u767C\u751F\u610F\u5916,\u9EBB\u7169\u4F60\u628A\u7260\u5011\u9A45\u8D95\u8D70\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: By the way, there seem to be a few slimes wandering around the exorcism
          platform. To prevent any accidents at the exorcism platform, could you
          please drive them away?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5C0D\u4E86,\u5916\u9762\u597D\u50CF\u6709\u5E7E\u96BB\u53F2\u840A\u59C6\u5728\u9A45\u9B54\u53F0\u9644\u8FD1\u9592\u6643,\u70BA\u4E86\u907F\u514D\u9A45\u9B54\u53F0\u767C\u751F\u610F\u5916,\u9EBB\u7169\u4F60\u628A\u7260\u5011\u9A45\u8D95\u8D70\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5BF9\u4E86,\u5916\u9762\u597D\u50CF\u6709\u51E0\u53EA\u53F2\u83B1\u59C6\u5728\u9A71\u9B54\u53F0\u9644\u8FD1\u95F2\u6643,\u4E3A\u4E86\u907F\u514D\u9A71\u9B54\u53F0\u53D1\u751F\u610F\u5916,\u9EBB\u70E6\u4F60\u628A\u5B83\u4EEC\u9A71\u8D76\u8D70\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u305D\u3046\u3060\u3001\u5916\u306B\u30B9\u30E9\u30A4\u30E0\u304C\u4F55\u5339\u304B\u9664\u970A\u53F0\u306E\u8FD1\u304F\u3092\u3046\u308D\u3064\u3044\u3066\u308B\u307F\u305F\u3044\u3060\u304B\u3089\u3001\u9664\u970A\u53F0\u3067\u4F55\u304B\u8D77\u3053\u308B\u306E\u3092\u9632\u3050\u305F\u3081\u306B\u3001\u8FFD\u3044\u6255\u3063\u3066\u304F\u308C\u308B\u304B\u3057\u3089\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 42
        destinationConversationID: 8
        destinationDialogueID: 43
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 284.52383
        y: 550.5569
        width: 160
        height: 30
    - id: 43
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u77E5\u9053\u4E86,\u6211\u96E2\u958B\u5F8C\u5C31\u53BB\u8655\u7406\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Understood, I'll handle it later.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u77E5\u9053\u4E86,\u6211\u96E2\u958B\u5F8C\u5C31\u53BB\u8655\u7406\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u77E5\u9053\u4E86,\u6211\u79BB\u5F00\u540E\u5C31\u53BB\u5904\u7406\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u308F\u304B\u3063\u305F\u3001\u5F8C\u3067\u5BFE\u5FDC\u3059\u308B\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 43
        destinationConversationID: 8
        destinationDialogueID: 44
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 284.52383
        y: 607.1979
        width: 160
        height: 30
    - id: 44
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5982\u679C\u8DEF\u4E0A\u9047\u5230\u5371\u96AA\u7684\u9B54\u7269\u5C31\u8D95\u7DCA\u56DE\u4F86,\u77E5\u9053\u55CE\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: If you encounter dangerous monsters on the road, come back quickly,
          okay?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5982\u679C\u8DEF\u4E0A\u9047\u5230\u5371\u96AA\u7684\u9B54\u7269\u5C31\u8D95\u7DCA\u56DE\u4F86,\u77E5\u9053\u55CE,\u5ABD\u5ABD\u4E0D\u5E0C\u671B\u4F60\u767C\u751F\u4EC0\u9EBC\u610F\u5916\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5982\u679C\u8DEF\u4E0A\u9047\u5230\u5371\u9669\u7684\u9B54\u7269\u5C31\u8D76\u7D27\u56DE\u6765,\u77E5\u9053\u5417,\u5988\u5988\u4E0D\u5E0C\u671B\u4F60\u53D1\u751F\u4EC0\u4E48\u610F\u5916\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3082\u3057\u9053\u3067\u5371\u306A\u3044\u9B54\u7269\u306B\u51FA\u4F1A\u3063\u305F\u3089\u3001\u3059\u3050\u306B\u623B\u3063\u3066\u304D\u3066\u306D\u3002\u4F55\u304B\u3042\u3063\u3066\u307B\u3057\u304F\u306A\u3044\u304B\u3089\u3001\u5206\u304B\u3063\u305F\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 44
        destinationConversationID: 8
        destinationDialogueID: 45
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 282.68896
        y: 664.2352
        width: 160
        height: 30
    - id: 45
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6211\u6703\u591A\u52A0\u6CE8\u610F\u7684,\u90A3\u6211\u5C31\u5148\u8D70\u56C9\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I'll be extra careful, I'll be going now.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6211\u6703\u591A\u52A0\u6CE8\u610F\u7684,\u90A3\u6211\u5C31\u5148\u8D70\u56C9\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6211\u4F1A\u591A\u52A0\u6CE8\u610F\u7684,\u90A3\u6211\u5C31\u5148\u8D70\u5570\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6C17\u3092\u4ED8\u3051\u308B\u3088\u3046\u306B\u3057\u307E\u3059\u3002\u3067\u306F\u3001\u884C\u3063\u3066\u304D\u307E\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 8
        originDialogueID: 45
        destinationConversationID: 8
        destinationDialogueID: 46
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 286.96286
        y: 718.17346
        width: 160
        height: 30
    - id: 46
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 3
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6069,\u8DEF\u4E0A\u5C0F\u5FC3\u5594~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: '{{default}};

          required CinemachinePriority(Ch0_ML_2_Cam,
          0)@{{end}};'
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Be careful on your way~
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6069,\u8DEF\u4E0A\u5C0F\u5FC3\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6069,\u8DEF\u4E0A\u5C0F\u5FC3\u5594~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3046\u3093\u3001\u6C17\u3092\u3064\u3051\u3066\u306D\uFF5E"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 8
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 281.14502
        y: 782.5167
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 637.3983}
    canvasZoom: 0.8200002
  - id: 9
    fields:
    - title: Title
      value: Interactable_object
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Parenthetical
        value: 
        type: 0
        typeString: 
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: 
      - title: EventGuid
        value: 
        type: 0
        typeString: 
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: 
      conversationID: 9
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 9
        originDialogueID: 0
        destinationConversationID: 9
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      - originConversationID: 9
        originDialogueID: 0
        destinationConversationID: 9
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      - originConversationID: 9
        originDialogueID: 0
        destinationConversationID: 9
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      - originConversationID: 9
        originDialogueID: 0
        destinationConversationID: 9
        destinationDialogueID: 9
        isConnector: 0
        priority: 2
      - originConversationID: 9
        originDialogueID: 0
        destinationConversationID: 9
        destinationDialogueID: 11
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 391.9588
        y: 25.87629
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9019\u662F\u5ABD\u5ABD\u7684\u623F\u9593,\u5982\u679C\u6C92\u4E8B\u5C31\u4E0D\u8981\u9032\u53BB\u4E86\u5427\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: This is Stepmom's private space, so please avoid entering unless necessary.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9019\u662F\u5ABD\u5ABD\u7684\u623F\u9593,\u5982\u679C\u6C92\u4E8B\u5C31\u4E0D\u8981\u9032\u53BB\u4E86\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u8FD9\u662F\u5988\u5988\u7684\u623F\u95F4,\u5982\u679C\u6CA1\u4E8B\u5C31\u4E0D\u8981\u8FDB\u53BB\u4E86\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6BCD\u3055\u3093\u306E\u90E8\u5C4B\u3060\u304B\u3089\u3001\u7528\u4E8B\u304C\u306A\u3044\u6642\u306F\u5165\u3089\u306A\u3044\u3067\u306D"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 9
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 285.20227
        y: 141.02524
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u66F8\u67B6\u4E0A\u653E\u7F6E\u8457\u5F88\u591A\u4E0D\u540C\u7A2E\u985E\u7684\u66F8,\u88E1\u9762\u7D55\u5927\u591A\u6578\u7684\u5167\u5BB9\u90FD\u5F88\u7121\u8DA3,\u9664\u4E86\u9019\u672C\u300C\u60E1\u9B54\u6703\u5632\u7B11\u300D\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: There are many different kinds of books on the bookshelf, but most
          of them are quite boring, except for this one, "Devil's Laughter."
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u66F8\u67B6\u4E0A\u653E\u7F6E\u8457\u5F88\u591A\u4E0D\u540C\u7A2E\u985E\u7684\u66F8,\u88E1\u9762\u7D55\u5927\u591A\u6578\u7684\u5167\u5BB9\u90FD\u5F88\u7121\u8DA3,\u9664\u4E86\u9019\u672C\u300C\u60E1\u9B54\u6703\u5632\u7B11\u300D\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u4E66\u67B6\u4E0A\u653E\u7F6E\u7740\u5F88\u591A\u4E0D\u540C\u79CD\u7C7B\u7684\u4E66,\u91CC\u9762\u7EDD\u5927\u591A\u6570\u7684\u5185\u5BB9\u90FD\u5F88\u65E0\u8DA3,\u9664\u4E86\u8FD9\u672C\u300C\u9B3C\u7B11\u300D\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u66F8\u68DA\u306B\u306F\u305F\u304F\u3055\u3093\u306E\u7A2E\u985E\u306E\u672C\u304C\u4E26\u3093\u3067\u3044\u308B\u304C\u3001\u307B\u3068\u3093\u3069\u304C\u3064\u307E\u3089\u306A\u3044\u5185\u5BB9\u3067\u3001\u3053\u306E\u300C\u30C7\u30D3\u30EB\u30CE\u30EF\u30E9\u30A6\u300D\u3060\u3051\u304C\u9762\u767D\u3044\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 9
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 9
        originDialogueID: 3
        destinationConversationID: 9
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 459.98203
        y: 139.10608
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u539F\u672C\u9084\u5F88\u671F\u5F85\u5F8C\u7E8C\u7684\u5167\u5BB9\uFF0C\u4E0D\u904E\u807D\u8AAA\u56E0\u4E0D\u660E\u539F\u56E0\u505C\u6B62\u767C\u552E\u4E86\uFF0C\u771F\u662F\u53EF\u60DC\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I was really looking forward to the follow-up content, but I heard
          that it has been discontinued for unknown reasons, which is a shame.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u539F\u672C\u9084\u5F88\u671F\u5F85\u5F8C\u7E8C\u7684\u5167\u5BB9\uFF0C\u4E0D\u904E\u807D\u8AAA\u56E0\u4E0D\u660E\u539F\u56E0\u505C\u6B62\u767C\u552E\u4E86\uFF0C\u771F\u662F\u53EF\u60DC\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u539F\u672C\u8FD8\u5F88\u671F\u5F85\u540E\u7EED\u7684\u5185\u5BB9\uFF0C\u4E0D\u8FC7\u542C\u8BF4\u56E0\u4E0D\u660E\u539F\u56E0\u65AD\u66F4\u4E86\uFF0C\u771F\u662F\u53EF\u60DC\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u7D9A\u304D\u304C\u697D\u3057\u307F\u3060\u3063\u305F\u306E\u306B\u3001\u4E0D\u660E\u306A\u7406\u7531\u3067\u767A\u58F2\u4E2D\u6B62\u306B\u306A\u3063\u305F\u3068\u805E\u3044\u3066\u6B8B\u5FF5\u3067\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 9
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 463.46432
        y: 298.37506
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u51B0\u7BB1\u96D6\u7136\u4E0D\u80FD\u50CF\u5132\u7269\u6212\u4E00\u6A23\u6C38\u4E45\u4FDD\u5B58\u6771\u897F,\u4E0D\u904E\u5728\u964D\u6EAB\u9019\u584A\u4E0A\u5012\u662F\u9059\u9059\u9818\u5148\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Although the refrigerator cannot store things permanently like a storage
          ring, it is far ahead in terms of cooling.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u51B0\u7BB1\u96D6\u7136\u4E0D\u80FD\u50CF\u5132\u7269\u6212\u4E00\u6A23\u6C38\u4E45\u4FDD\u5B58\u6771\u897F,\u4E0D\u904E\u5728\u964D\u6EAB\u9019\u584A\u4E0A\u5012\u662F\u9059\u9059\u9818\u5148\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u51B0\u7BB1\u867D\u7136\u4E0D\u80FD\u50CF\u50A8\u7269\u6212\u4E00\u6837\u6C38\u4E45\u4FDD\u5B58\u4E1C\u897F,\u4E0D\u8FC7\u5728\u964D\u6E29\u8FD9\u5757\u4E0A\u5012\u662F\u9065\u9065\u9886\u5148\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u51B7\u8535\u5EAB\u306F\u53CE\u7D0D\u30EA\u30F3\u30B0\u306E\u3088\u3046\u306B\u6C38\u4E45\u7684\u306B\u7269\u3092\u4FDD\u5B58\u3059\u308B\u3053\u3068\u306F\u3067\u304D\u306A\u3044\u304C\u3001\u51B7\u5374\u6027\u80FD\u306B\u304A\u3044\u3066\u306F\u306F\u308B\u304B\u306B\u512A\u308C\u3066\u3044\u308B\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 9
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 112.34021
        y: 140.75122
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5287\u60C5\u8B1B\u8FF0\u4E00\u500B\u7D05\u8272\u60E1\u9B54\u8207\u4E00\u500B\u85CD\u8272\u60E1\u9B54\u70BA\u4E86\u722D\u596A\u529B\u91CF\u800C\u5C55\u958B\u4E86\u7121\u6578\u6B21\u6230\u9B25\uFF0C\u4E26\u5728\u6230\u9B25\u7684\u904E\u7A0B\u4E2D\u4E0D\u5C0F\u5FC3\u62EF\u6551\u4E16\u754C\u7684\u6545\u4E8B\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The plot tells the story of a red devil and a blue devil who fought
          countless battles for power and accidentally saved the world during the
          battle.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5287\u60C5\u8B1B\u8FF0\u4E00\u500B\u7D05\u8272\u60E1\u9B54\u8207\u4E00\u500B\u85CD\u8272\u60E1\u9B54\u70BA\u4E86\u722D\u596A\u529B\u91CF\u800C\u5C55\u958B\u4E86\u7121\u6578\u6B21\u6230\u9B25\uFF0C\u4E26\u5728\u6230\u9B25\u7684\u904E\u7A0B\u4E2D\u4E0D\u5C0F\u5FC3\u62EF\u6551\u4E16\u754C\u7684\u6545\u4E8B\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5267\u60C5\u8BB2\u8FF0\u4E00\u4E2A\u7EA2\u8272\u6076\u9B54\u4E0E\u4E00\u4E2A\u84DD\u8272\u6076\u9B54\u4E3A\u4E86\u4E89\u593A\u529B\u91CF\u800C\u5C55\u5F00\u4E86\u65E0\u6570\u6B21\u6218\u6597\uFF0C\u5E76\u5728\u6218\u6597\u7684\u8FC7\u7A0B\u4E2D\u4E0D\u5C0F\u5FC3\u62EF\u6551\u4E16\u754C\u7684\u6545\u4E8B\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3053\u306E\u7269\u8A9E\u306F\u3001\u8D64\u3044\u60AA\u9B54\u3068\u9752\u3044\u60AA\u9B54\u304C\u529B\u3092\u5DE1\u3063\u3066\u4F55\u5EA6\u3082\u6226\u3044\u3092\u7E70\u308A\u5E83\u3052\u308B\u4E2D\u3067\u3001\u6226\u3044\u306E\u904E\u7A0B\u3067\u3046\u3063\u304B\u308A\u4E16\u754C\u3092\u6551\u3063\u3066\u3057\u307E\u3046\u3068\u3044\u3046\u30B9\u30C8\u30FC\u30EA\u30FC\u3092\u63CF\u3044\u3066\u3044\u307E\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 9
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 9
        originDialogueID: 7
        destinationConversationID: 9
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 456.0034
        y: 225.74976
        width: 160
        height: 30
    - id: 9
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6D74\u5BA4\u7684\u9580\u6700\u8FD1\u6642\u5E38\u95DC\u4E0D\u7DCA,\u5FC5\u9808\u8981\u8D95\u7DCA\u627E\u500B\u6642\u9593\u4FEE\u7406\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The bathroom door often doesn't close tightly recently, so I have
          to find a time to repair it quickly.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6D74\u5BA4\u7684\u9580\u6700\u8FD1\u6642\u5E38\u95DC\u4E0D\u7DCA,\u5FC5\u9808\u8981\u8D95\u7DCA\u627E\u500B\u6642\u9593\u4FEE\u7406\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6D74\u5BA4\u7684\u95E8\u6700\u8FD1\u65F6\u5E38\u5173\u4E0D\u7D27,\u5FC5\u987B\u8981\u8D76\u7D27\u627E\u4E2A\u65F6\u95F4\u4FEE\u7406\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6700\u8FD1\u3001\u6D74\u5BA4\u306E\u30C9\u30A2\u304C\u3046\u307E\u304F\u9589\u307E\u3089\u306A\u304F\u306A\u3063\u3066\u304D\u3066\u3001\u65E9\u304F\u4FEE\u7406\u3057\u306A\u3044\u3068\u3044\u3051\u306A\u3044\u3067\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 9
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 643.6712
        y: 143.3576
        width: 160
        height: 30
    - id: 11
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5ABD\u5ABD\u8AAA\u7684\u9999\u5C31\u653E\u5728\u9019\u88E1......"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The incense sticks that stepmom mentioned is placed here...
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5ABD\u5ABD\u8AAA\u7684\u9999\u5C31\u653E\u5728\u9019\u88E1......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5988\u5988\u8BF4\u7684\u9999\u5C31\u653E\u5728\u8FD9\u91CC......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6BCD\u3055\u3093\u304C\u8A00\u3063\u3066\u3044\u305F\u9999\u308A\u306F\u3001\u3053\u3053\u306B\u7F6E\u3044\u3066\u3042\u308A\u307E\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 9
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 9
        originDialogueID: 11
        destinationConversationID: 9
        destinationDialogueID: 12
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 825.9612
        y: 135.75444
        width: 160
        height: 30
    - id: 12
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u76EE\u524D\u7248\u672C\u7684\u904A\u6232\u9032\u5EA6\u5C31\u5230\u9019\u908A,\u611F\u8B1D\u5927\u5BB6\u904A\u73A9~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The current version of the game ends here. Thank you all for playing!
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u76EE\u524D\u7248\u672C\u7684\u904A\u6232\u9032\u5EA6\u5C31\u5230\u9019\u908A,\u611F\u8B1D\u5927\u5BB6\u904A\u73A9~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u76EE\u524D\u7248\u672C\u7684\u6E38\u620F\u8FDB\u5EA6\u5C31\u5230\u8FD9\u8FB9,\u611F\u8C22\u5927\u5BB6\u6E38\u73A9~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u4ECA\u30D0\u30FC\u30B8\u30E7\u30F3\u3067\u306E\u30A2\u30C3\u30D7\u30C7\u30FC\u30C8\u306F\u3053\u3053\u307E\u3067\u3068\u306A\u308A\u307E\u3059\u3002\u7686\u69D8\u306E\u3054\u5354\u529B\u306B\u5FC3\u3088\u308A\u611F\u8B1D\u3044\u305F\u3057\u307E\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 9
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 828.4133
        y: 209.68533
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 32.805984, y: 0}
    canvasZoom: 0.87999994
  - id: 10
    fields:
    - title: Title
      value: Ch0_ML_CS_002_Anim
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: -1
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: None()
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 10
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 10
        originDialogueID: 0
        destinationConversationID: 10
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 388.94727
        y: 36.402206
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6211\u8A18\u5F97\u9999\u5728\u9019\u9644\u8FD1......"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: I remember the incense was around here...
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6211\u8A18\u5F97\u9999\u5728\u9019\u9644\u8FD1......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6211\u8BB0\u5F97\u9999\u5728\u8FD9\u9644\u8FD1......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3053\u306E\u8FBA\u308A\u306B\u7DDA\u9999\u304C\u3057\u305F\u306F\u305A......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 10
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 10
        originDialogueID: 1
        destinationConversationID: 10
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 386.76733
        y: 134.26154
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6B38?\u9019\u662F\u4EC0\u9EBC?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Huh? What is this?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6B38?\u9019\u662F\u4EC0\u9EBC?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6B38?\u8FD9\u662F\u4EC0\u4E48?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3048\u3001\u4F55\u3053\u308C\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 10
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 10
        originDialogueID: 3
        destinationConversationID: 10
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 382.44162
        y: 208.39914
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5F9E\u4F86\u6C92\u6709\u770B\u904E\u7684\u76D2\u5B50......"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: "A box I\u2019ve never seen before\u2026"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5F9E\u4F86\u6C92\u6709\u770B\u904E\u7684\u76D2\u5B50......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u4ECE\u6765\u6CA1\u6709\u770B\u8FC7\u7684\u76D2\u5B50......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u898B\u305F\u3053\u3068\u306E\u306A\u3044\u7BB1\u2026\u2026"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 10
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 10
        originDialogueID: 4
        destinationConversationID: 10
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 384.48932
        y: 280.86774
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u54B3\u54B3......\u9019\u4EC0\u9EBC\u9B3C?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Ahem...what the hell is this?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u54B3\u54B3......\u9019\u4EC0\u9EBC?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u54B3\u54B3......\u641E\u4EC0\u4E48?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u30B3\u30DB\u30F3\u3001\u30B3\u30DB\u30F3\u2026\u2026\u306A\u3093\u3060\u3088\u3001\u3053\u308C\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 10
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 10
        originDialogueID: 5
        destinationConversationID: 10
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 392.26138
        y: 348.45038
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u7ADF\u7136\u662F\u7A7A\u7684?"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: It's empty?
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u7ADF\u7136\u662F\u7A7A\u7684?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u7ADF\u7136\u662F\u7A7A\u7684?"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3048\u3001\u7A7A\u3063\u307D\u306A\u306E\uFF1F"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 10
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 10
        originDialogueID: 6
        destinationConversationID: 10
        destinationDialogueID: 7
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 397.11694
        y: 416.469
        width: 160
        height: 30
    - id: 7
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5728\u53CD\u8986\u6478\u7D22\u5F8C,\u78BA\u5B9A\u76D2\u5B50\u5167\u78BA\u5BE6\u6C92\u6709\u4EFB\u4F55\u6A5F\u95DC\u6216\u593E\u5C64\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: After repeated exploration, it was determined that there were indeed
          no mechanisms or mezzanines in the box.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u5728\u53CD\u8986\u6478\u7D22\u5F8C,\u78BA\u5B9A\u76D2\u5B50\u5167\u78BA\u5BE6\u6C92\u6709\u4EFB\u4F55\u6A5F\u95DC\u6216\u593E\u5C64\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u5728\u53CD\u8986\u6478\u7D22\u540E,\u786E\u5B9A\u76D2\u5B50\u5185\u786E\u5B9E\u6CA1\u6709\u4EFB\u4F55\u673A\u5173\u6216\u5939\u5C42\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u4F55\u5EA6\u3082\u8ABF\u3079\u305F\u7D50\u679C\u3001\u7BB1\u306E\u4E2D\u306B\u306F\u4ED5\u639B\u3051\u3084\u96A0\u3057\u90E8\u5C4B\u306A\u3069\u306F\u4E00\u5207\u306A\u3044\u3053\u3068\u304C\u78BA\u8A8D\u3067\u304D\u305F\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Display Name en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: Display Name jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      conversationID: 10
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 393.11954
        y: 484.4779
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 0.6100002
  - id: 11
    fields:
    - title: Title
      value: ch0_ML_Default_002
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Parenthetical
        value: 
        type: 0
        typeString: 
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: 
      - title: EventGuid
        value: 
        type: 0
        typeString: 
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: 
      conversationID: 11
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 11
        originDialogueID: 0
        destinationConversationID: 11
        destinationDialogueID: 1
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 402.45285
        y: 16.79245
        width: 160
        height: 30
    - id: 1
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6069......\u9019\u771F\u7684\u5C31\u53EA\u662F\u500B\u7A7A\u76D2\u5B50\u800C\u5DF2\uFF0C\u6C92\u4EC0\u9EBC\u7279\u5225\u7684\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Well...it's really just an empty box, nothing special.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u6069......\u9019\u771F\u7684\u5C31\u53EA\u662F\u500B\u7A7A\u76D2\u5B50\u800C\u5DF2\uFF0C\u6C92\u4EC0\u9EBC\u7279\u5225\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u6069......\u8FD9\u771F\u7684\u5C31\u53EA\u662F\u4E2A\u7A7A\u76D2\u5B50\u800C\u5DF2\uFF0C\u6CA1\u4EC0\u4E48\u7279\u522B\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3046\u3093\u2026\u2026\u3053\u308C\u306F\u672C\u5F53\u306B\u305F\u3060\u306E\u7A7A\u7BB1\u3067\u3001\u7279\u306B\u4F55\u3082\u306A\u3055\u305D\u3046\u3060\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 11
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 11
        originDialogueID: 1
        destinationConversationID: 11
        destinationDialogueID: 2
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 399.20706
        y: 128.05688
        width: 160
        height: 30
    - id: 2
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u4F30\u8A08\u662F\u5ABD\u5ABD\u5728\u5916\u9762\u64BF\u5230\u7684\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Probably Stepmom picked it up outside.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u4F30\u8A08\u662F\u5ABD\u5ABD\u5728\u5916\u9762\u64BF\u5230\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u4F30\u8BA1\u662F\u5988\u5988\u5728\u5916\u9762\u6361\u5230\u7684\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u304A\u305D\u3089\u304F\u3001\u6BCD\u3055\u3093\u304C\u5916\u3067\u62FE\u3063\u3066\u304D\u305F\u3093\u3060\u308D\u3046\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 11
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 11
        originDialogueID: 2
        destinationConversationID: 11
        destinationDialogueID: 3
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 401.0813
        y: 197.48825
        width: 160
        height: 30
    - id: 3
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u96D6\u7136\u4E0D\u77E5\u9053\u5ABD\u5ABD\u6253\u7B97\u7528\u5B83\u4F86\u505A\u4EC0\u9EBC\uFF0C\u4E0D\u904E\u9084\u662F\u8D95\u7DCA\u653E\u56DE\u53BB\u5427\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Even though I don't know what Stepmom plans to do with it, I should
          put it back quickly.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u96D6\u7136\u4E0D\u77E5\u9053\u5ABD\u5ABD\u6253\u7B97\u7528\u5B83\u4F86\u505A\u4EC0\u9EBC\uFF0C\u4E0D\u904E\u9084\u662F\u8D95\u7DCA\u653E\u56DE\u53BB\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u867D\u7136\u4E0D\u77E5\u9053\u5988\u5988\u6253\u7B97\u7528\u5B83\u6765\u505A\u4EC0\u4E48\uFF0C\u4E0D\u8FC7\u8FD8\u662F\u8D76\u7D27\u653E\u56DE\u53BB\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u6BCD\u3055\u3093\u304C\u3053\u308C\u3067\u4F55\u3092\u3059\u308B\u3064\u3082\u308A\u306A\u306E\u304B\u5206\u304B\u3089\u306A\u3044\u3051\u3069\u3001\u3068\u308A\u3042\u3048\u305A\u5143\u306E\u5834\u6240\u306B\u623B\u3057\u3066\u304A\u3053\u3046\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 11
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 11
        originDialogueID: 3
        destinationConversationID: 11
        destinationDialogueID: 4
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 403.39838
        y: 266.95557
        width: 160
        height: 30
    - id: 4
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u73FE\u5728\u6700\u91CD\u8981\u7684\u662F\u8D95\u7DCA\u627E\u5230\u9999\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The most important thing right now is to find the incense quickly.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u73FE\u5728\u6700\u91CD\u8981\u7684\u662F\u8D95\u7DCA\u627E\u5230\u9999\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u73B0\u5728\u6700\u91CD\u8981\u7684\u662F\u8D76\u7D27\u627E\u5230\u9999\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u4ECA\u4E00\u756A\u5927\u4E8B\u306A\u306E\u306F\u3001\u65E9\u304F\u7DDA\u9999\u3092\u898B\u3064\u3051\u308B\u3053\u3068\u3060\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 11
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 11
        originDialogueID: 4
        destinationConversationID: 11
        destinationDialogueID: 5
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 403.7622
        y: 338.3308
        width: 160
        height: 30
    - id: 5
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9999......\u5728\u9019\u88E1\uFF01\u627E\u5230\u4E86\uFF0C\u9084\u6709\u5176\u4ED6\u796D\u5960\u9700\u8981\u7684\u6771\u897F\u4E5F\u8981\u5E36\u8457\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The incense... it's here! I found it. And I'll take the other things
          needed for the ritual too.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9999......\u5728\u9019\u88E1\uFF01\u627E\u5230\u4E86\uFF0C\u9084\u6709\u5176\u4ED6\u796D\u5960\u9700\u8981\u7684\u6771\u897F\u4E5F\u8981\u5E36\u8457\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u9999......\u5728\u8FD9\u91CC\uFF01\u627E\u5230\u4E86\uFF0C\u8FD8\u6709\u5176\u4ED6\u796D\u5960\u9700\u8981\u7684\u4E1C\u897F\u4E5F\u8981\u5E26\u7740\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u7DDA\u9999\u2026\u2026\u3053\u3053\u306B\u3042\u3063\u305F\uFF01\u898B\u3064\u3051\u305F\u3001\u4ED6\u306E\u796D\u58C7\u306B\u5FC5\u8981\u306A\u7269\u3082\u6301\u3063\u3066\u884C\u3053\u3046\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 11
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 11
        originDialogueID: 5
        destinationConversationID: 11
        destinationDialogueID: 6
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 395.50534
        y: 402.4959
        width: 160
        height: 30
    - id: 6
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u597D\uFF01\u6771\u897F\u90FD\u6E96\u5099\u5F97\u5DEE\u4E0D\u591A\u4E86\uFF0C\u8DDF\u5ABD\u5ABD\u8AAA\u4E00\u8072\u5427\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: Alright! Everything is almost ready, let's go tell Stepmom.
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u597D\uFF01\u6771\u897F\u90FD\u6E96\u5099\u5F97\u5DEE\u4E0D\u591A\u4E86\uFF0C\u53BB\u8DDF\u5ABD\u5ABD\u8AAA\u4E00\u8072\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u597D\uFF01\u4E1C\u897F\u90FD\u51C6\u5907\u5F97\u5DEE\u4E0D\u591A\u4E86\uFF0C\u53BB\u8DDF\u5988\u5988\u8BF4\u4E00\u58F0\u5427\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3088\u3057\uFF01\u6E96\u5099\u306F\u307B\u307C\u3067\u304D\u305F\u304B\u3089\u3001\u6BCD\u3055\u3093\u306B\u58F0\u3092\u304B\u3051\u3066\u304A\u3053\u3046\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: EventGuid
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 11
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 396.42276
        y: 476.5785
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 0.8200001
  - id: 12
    fields:
    - title: Title
      value: ch0_ML_Default_003
      type: 0
      typeString: CustomFieldType_Text
    - title: Pictures
      value: '[]'
      type: 3
      typeString: CustomFieldType_Files
    - title: Description
      value: 
      type: 0
      typeString: CustomFieldType_Text
    - title: Actor
      value: 2
      type: 5
      typeString: CustomFieldType_Actor
    - title: Conversant
      value: 1
      type: 5
      typeString: CustomFieldType_Actor
    overrideSettings:
      useOverrides: 0
      overrideSubtitleSettings: 0
      showNPCSubtitlesDuringLine: 1
      showNPCSubtitlesWithResponses: 1
      showPCSubtitlesDuringLine: 0
      skipPCSubtitleAfterResponseMenu: 0
      subtitleCharsPerSecond: 30
      minSubtitleSeconds: 2
      continueButton: 0
      overrideSequenceSettings: 0
      defaultSequence: 
      defaultPlayerSequence: 
      defaultResponseMenuSequence: 
      overrideInputSettings: 0
      alwaysForceResponseMenu: 1
      includeInvalidEntries: 0
      responseTimeout: 0
      emTagForOldResponses: 0
      emTagForInvalidResponses: 0
      cancelSubtitle:
        key: 27
        buttonName: 
      cancelConversation:
        key: 27
        buttonName: 
    nodeColor: 
    dialogueEntries:
    - id: 0
      fields:
      - title: Title
        value: START
        type: 0
        typeString: 
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: 
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: 
      - title: Dialogue Text
        value: 
        type: 0
        typeString: 
      - title: Parenthetical
        value: 
        type: 0
        typeString: 
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: 
      - title: Sequence
        value: None()
        type: 0
        typeString: 
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: 
      - title: EventGuid
        value: 
        type: 0
        typeString: 
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: 
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 12
        originDialogueID: 0
        destinationConversationID: 12
        destinationDialogueID: 17
        isConnector: 0
        priority: 2
      - originConversationID: 12
        originDialogueID: 0
        destinationConversationID: 12
        destinationDialogueID: 22
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 186
        y: 30
        width: 160
        height: 30
    - id: 17
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9019\u5C31\u662F\u7236\u89AA\u8207\u6BCD\u89AA\u7684\u58B3\u5893......"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: This is the grave of father and mother...
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9019\u5C31\u662F\u7236\u89AA\u8207\u6BCD\u89AA\u7684\u58B3\u5893......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u8FD9\u5C31\u662F\u7236\u4EB2\u4E0E\u6BCD\u4EB2\u7684\u575F\u5893......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3053\u3053\u304C\u7236\u3068\u6BCD\u306E\u304A\u5893\u304B\u2026\u2026"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 12
        originDialogueID: 17
        destinationConversationID: 12
        destinationDialogueID: 18
        isConnector: 0
        priority: 2
      conditionsString: "CurrentQuestState(\"\u524D\u5F80\u5893\u5712\") == \"active\""
      userScript: "SetQuestState(\"\u524D\u5F80\u5893\u5712\", \"success\")"
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 67.46472
        y: 122.299484
        width: 160
        height: 30
    - id: 18
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u76EE\u524D\u7248\u672C\u7684\u904A\u6232\u9032\u5EA6\u5C31\u5230\u9019\u908A,\u611F\u8B1D\u5927\u5BB6\u904A\u73A9~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The current version of the game ends here. Thank you all for playing!
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u76EE\u524D\u7248\u672C\u7684\u904A\u6232\u9032\u5EA6\u5C31\u5230\u9019\u908A,\u611F\u8B1D\u5927\u5BB6\u904A\u73A9~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u76EE\u524D\u7248\u672C\u7684\u6E38\u620F\u8FDB\u5EA6\u5C31\u5230\u8FD9\u8FB9,\u611F\u8C22\u5927\u5BB6\u6E38\u73A9~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u4ECA\u30D0\u30FC\u30B8\u30E7\u30F3\u3067\u306E\u30A2\u30C3\u30D7\u30C7\u30FC\u30C8\u306F\u3053\u3053\u307E\u3067\u3068\u306A\u308A\u307E\u3059\u3002\u7686\u69D8\u306E\u3054\u5354\u529B\u306B\u5FC3\u3088\u308A\u611F\u8B1D\u3044\u305F\u3057\u307E\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 63.97705
        y: 192.51512
        width: 160
        height: 30
    - id: 19
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u96D6\u7136\u767C\u751F\u4E86\u4E00\u4E9B\u4EE4\u5C0F\u6B66\u60F3\u4E0D\u900F\u7684\u602A\u4E8B\uFF0C\u4F46\u90A3\u4E9B\u90FD\u662F\u7E41\u7463\u5C0F\u4E8B\uFF0C\u5C0F\u6B66\u81EA\u884C\u8655\u7406\u4FBF\u597D\uFF0C\u5C31\u4E0D\u52DE\u7169\u5728\u5929\u908A\u4EAB\u798F\u7684\u4E8C\u4F4D\u639B\u5FC3\u4E86\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 12
        originDialogueID: 19
        destinationConversationID: 12
        destinationDialogueID: 20
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 76.69455
        y: 264.29404
        width: 160
        height: 30
    - id: 20
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u7531\u7F8E\u5B50\u963F\u59E8\u8AAA\u904E\u4E0B\u5468\u672B\u6703\u4E0B\u5C71\u53C3\u52A0\u82B1\u5349\u6BD4\u8CFD\u7684\u5FB5\u9078\uFF0C\u5230\u6642\u5019\u6703\u518D\u89AA\u81EA\u524D\u4F86\u8DDF\u5169\u4F4D\u8AAA\u660E\uFF0C\u5C0F\u6B66\u5148\u63D0\u524D\u77E5\u6703\u5169\u4F4D\u4E00\u8072\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 12
        originDialogueID: 20
        destinationConversationID: 12
        destinationDialogueID: 21
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 67.991486
        y: 334.1862
        width: 160
        height: 30
    - id: 21
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5FB5\u9078\u671F\u9593\u4E5F\u8ACB\u5169\u4F4D\u5E6B\u5FD9\u4FDD\u4F51\u7531\u7F8E\u5B50\u963F\u59E8\uFF0C\u4E0D\u8981\u8B93\u61F7\u6709\u7570\u5FC3\u7684\u7537\u6027\u80FD\u5920\u96A8\u610F\u9760\u8FD1\u7531\u7F8E\u5B50\u963F\u59E8\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 12
        originDialogueID: 21
        destinationConversationID: 12
        destinationDialogueID: 23
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 67.69037
        y: 397.86954
        width: 160
        height: 30
    - id: 22
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u9019\u5C31\u662F\u7236\u89AA\u8207\u6BCD\u89AA\u7684\u58B3\u5893......"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: This is the grave of father and mother...
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u9019\u5C31\u662F\u7236\u89AA\u8207\u6BCD\u89AA\u7684\u58B3\u5893......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u8FD9\u5C31\u662F\u7236\u4EB2\u4E0E\u6BCD\u4EB2\u7684\u575F\u5893......"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u3053\u3053\u304C\u7236\u3068\u6BCD\u306E\u304A\u5893\u304B\u2026\u2026"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 12
        originDialogueID: 22
        destinationConversationID: 12
        destinationDialogueID: 25
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 320.02292
        y: 127.66666
        width: 160
        height: 30
    - id: 23
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: 1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u6700\u5F8C\u9084\u8ACB\u5169\u4F4D\u80FD\u5920\u6301\u7E8C\u4FDD\u4F51\u5C0F\u6B66\u8207\u7531\u7F8E\u5B50\u963F\u59E8\uFF0C\u4E0D\u51FA\u5927\u4E8B\u3001\u4E0D\u751F\u5927\u75C5\uFF0C\u51E1\u4E8B\u9806\u9042\u5E73\u5B89\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks:
      - originConversationID: 12
        originDialogueID: 23
        destinationConversationID: 12
        destinationDialogueID: 24
        isConnector: 0
        priority: 2
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 64.49887
        y: 453.33493
        width: 160
        height: 30
    - id: 24
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u5C0F\u6B66\u4ECA\u5F8C\u4E5F\u6703\u7E7C\u7E8C\u7DAD\u8B77\u9019\u4E00\u584A\u74B0\u5883\uFF0C\u5B88\u8B77\u9019\u4E00\u65B9\u5929\u5730\uFF0C\u795D\u798F\u4E8C\u4F4D\u80FD\u5920\u5728\u5929\u908A\u4EAB\u798F\u3002"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: 
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 68.900635
        y: 524.4652
        width: 160
        height: 30
    - id: 25
      fields:
      - title: Title
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Pictures
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Description
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Actor
        value: 2
        type: 5
        typeString: CustomFieldType_Actor
      - title: Conversant
        value: -1
        type: 5
        typeString: CustomFieldType_Actor
      - title: Menu Text
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Dialogue Text
        value: "\u76EE\u524D\u7248\u672C\u7684\u904A\u6232\u9032\u5EA6\u5C31\u5230\u9019\u908A,\u611F\u8B1D\u5927\u5BB6\u904A\u73A9~"
        type: 0
        typeString: CustomFieldType_Text
      - title: Parenthetical
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Audio Files
        value: '[]'
        type: 3
        typeString: CustomFieldType_Files
      - title: Video File
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: en
        value: The current version of the game ends here. Thank you all for playing!
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-tw
        value: "\u76EE\u524D\u7248\u672C\u7684\u904A\u6232\u9032\u5EA6\u5C31\u5230\u9019\u908A,\u611F\u8B1D\u5927\u5BB6\u904A\u73A9~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: zh-cn
        value: "\u76EE\u524D\u7248\u672C\u7684\u6E38\u620F\u8FDB\u5EA6\u5C31\u5230\u8FD9\u8FB9,\u611F\u8C22\u5927\u5BB6\u6E38\u73A9~"
        type: 4
        typeString: CustomFieldType_Localization
      - title: jp
        value: "\u4ECA\u30D0\u30FC\u30B8\u30E7\u30F3\u3067\u306E\u30A2\u30C3\u30D7\u30C7\u30FC\u30C8\u306F\u3053\u3053\u307E\u3067\u3068\u306A\u308A\u307E\u3059\u3002\u7686\u69D8\u306E\u3054\u5354\u529B\u306B\u5FC3\u3088\u308A\u611F\u8B1D\u3044\u305F\u3057\u307E\u3059\u3002"
        type: 4
        typeString: CustomFieldType_Localization
      - title: canvasRect
        value: 
        type: 0
        typeString: CustomFieldType_Text
      - title: Response Menu Sequence
        value: 
        type: 0
        typeString: CustomFieldType_Text
      conversationID: 12
      isRoot: 0
      isGroup: 0
      nodeColor: 
      delaySimStatus: 0
      falseConditionAction: Block
      conditionPriority: 2
      outgoingLinks: []
      conditionsString: 
      userScript: 
      onExecute:
        m_PersistentCalls:
          m_Calls: []
      canvasRect:
        serializedVersion: 2
        x: 318.77246
        y: 195.5934
        width: 160
        height: 30
    entryGroups: []
    canvasScrollPosition: {x: 0, y: 0}
    canvasZoom: 0.85000014
  syncInfo:
    syncActors: 0
    syncItems: 0
    syncLocations: 0
    syncVariables: 0
    syncActorsDatabase: {fileID: 0}
    syncItemsDatabase: {fileID: 0}
    syncLocationsDatabase: {fileID: 0}
    syncVariablesDatabase: {fileID: 0}
  templateJson: '{"treatItemsAsQuests":true,"actorFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"IsPlayer","value":"False","type":2,"typeString":"CustomFieldType_Boolean"},{"title":"Display
    Name en","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name zh-tw","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name zh-cn","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name jp","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"NodeColor","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Display
    Name","value":"","type":0,"typeString":""}],"itemFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Is
    Item","value":"True","type":2,"typeString":"CustomFieldType_Boolean"}],"questFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Success
    Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Failure
    Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"State","value":"unassigned","type":0,"typeString":"CustomFieldType_QuestState"},{"title":"Is
    Item","value":"False","type":2,"typeString":"CustomFieldType_Boolean"},{"title":"","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Trackable","value":"False","type":2,"typeString":"CustomFieldType_Boolean"},{"title":"Track","value":"False","type":2,"typeString":"CustomFieldType_Boolean"},{"title":"Display
    Name en","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name zh-tw","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name zh-cn","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name jp","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Description
    en","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Description
    zh-tw","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Description
    zh-cn","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Description
    jp","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    Count","value":"0","type":1,"typeString":"CustomFieldType_Number"},{"title":"Entry
    2 State","value":"(None)","type":0,"typeString":"CustomFieldType_QuestState"},{"title":"Entry
    2","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Entry 2
    en","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    2 zh-tw","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    2 zh-cn","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    2 jp","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    1","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Entry 1
    State","value":"(None)","type":0,"typeString":"CustomFieldType_QuestState"},{"title":"Entry
    1 en","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    1 zh-tw","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    1 zh-cn","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    1 jp","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Entry
    3 State","value":"(None)","type":0,"typeString":"CustomFieldType_QuestState"},{"title":"Entry
    3","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Entry 3
    en","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    3 zh-tw","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    3 zh-cn","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Entry
    3 jp","value":"","type":4,"typeString":"CustomFieldType_Localization"}],"locationFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"}],"variableFields":[{"title":"Name","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Initial
    Value","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"}],"conversationFields":[{"title":"Title","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Actor","value":"0","type":5,"typeString":"CustomFieldType_Actor"},{"title":"Conversant","value":"0","type":5,"typeString":"CustomFieldType_Actor"}],"dialogueEntryFields":[{"title":"Title","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Pictures","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Description","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Actor","value":"","type":5,"typeString":"CustomFieldType_Actor"},{"title":"Conversant","value":"","type":5,"typeString":"CustomFieldType_Actor"},{"title":"Menu
    Text","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Dialogue
    Text","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Parenthetical","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Audio
    Files","value":"[]","type":3,"typeString":"CustomFieldType_Files"},{"title":"Video
    File","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Sequence","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"en","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"zh-tw","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"zh-cn","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"jp","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"canvasRect","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"EventGuid","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Response
    Menu Sequence","value":"","type":0,"typeString":"CustomFieldType_Text"},{"title":"Display
    Name jp","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name zh-tw","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name en","value":"","type":4,"typeString":"CustomFieldType_Localization"},{"title":"Display
    Name zh-cn","value":"","type":4,"typeString":"CustomFieldType_Localization"}],"actorPrimaryFieldTitles":["en","zh-tw","zh-cn","jp","Display
    Name en","Display Name zh-tw","Display Name zh-cn","Display Name jp"],"itemPrimaryFieldTitles":[],"questPrimaryFieldTitles":[],"locationPrimaryFieldTitles":[],"variablePrimaryFieldTitles":[],"conversationPrimaryFieldTitles":[],"dialogueEntryPrimaryFieldTitles":[],"npcLineColor":{"r":1.0,"g":0.0,"b":0.0,"a":1.0},"pcLineColor":{"r":0.0,"g":0.0,"b":1.0,"a":1.0},"repeatLineColor":{"r":0.5,"g":0.5,"b":0.5,"a":1.0}}'
