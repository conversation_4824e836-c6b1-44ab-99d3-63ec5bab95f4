using UnityEngine;
using Studio.Timeline;

namespace Studio.Timeline
{
    /// <summary>
    /// Test script to verify timeline fade functionality
    /// Attach this to a GameObject and call TestFade() to test the fade effects
    /// </summary>
    public class TimelineFadeTest : MonoBehaviour
    {
        [Header("Test Settings")]
        public TimelineTrigger timelineTrigger;
        
        [Space(10)]
        [Header("Manual Test Controls")]
        public KeyCode testFadeInKey = KeyCode.F1;
        public KeyCode testFadeOutKey = KeyCode.F2;
        public KeyCode testFullTimelineKey = KeyCode.F3;
        
        [Space(10)]
        [Header("Test Parameters")]
        public float testFadeDuration = 1f;
        
        private void Update()
        {
            // Manual testing controls
            if (Input.GetKeyDown(testFadeInKey))
            {
                TestFadeIn();
            }
            
            if (Input.GetKeyDown(testFadeOutKey))
            {
                TestFadeOut();
            }
            
            if (Input.GetKeyDown(testFullTimelineKey))
            {
                TestFullTimeline();
            }
        }
        
        /// <summary>
        /// Test fade in effect only
        /// </summary>
        public void TestFadeIn()
        {
            Debug.Log("Testing Fade In Effect");
            StartCoroutine(TestFadeInCoroutine());
        }
        
        /// <summary>
        /// Test fade out effect only
        /// </summary>
        public void TestFadeOut()
        {
            Debug.Log("Testing Fade Out Effect");
            StartCoroutine(TestFadeOutCoroutine());
        }
        
        /// <summary>
        /// Test full timeline with fade effects
        /// </summary>
        public void TestFullTimeline()
        {
            if (timelineTrigger != null)
            {
                Debug.Log("Testing Full Timeline with Fade Effects");
                timelineTrigger.TriggerDirector();
            }
            else
            {
                Debug.LogWarning("TimelineTrigger reference is not set!");
            }
        }
        
        private System.Collections.IEnumerator TestFadeInCoroutine()
        {
            // Set initial black screen
            CameraManager.SetStoryboardCameraAlpha(1f);
            
            // Fade in (from black to transparent)
            float elapsedTime = 0f;
            while (elapsedTime < testFadeDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(1f, 0f, elapsedTime / testFadeDuration);
                CameraManager.SetStoryboardCameraAlpha(alpha);
                yield return null;
            }
            
            // Ensure final alpha is 0 (fully transparent)
            CameraManager.SetStoryboardCameraAlpha(0f);
            Debug.Log("Fade In Test Complete");
        }
        
        private System.Collections.IEnumerator TestFadeOutCoroutine()
        {
            // Fade out (from transparent to black)
            float elapsedTime = 0f;
            while (elapsedTime < testFadeDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float alpha = Mathf.Lerp(0f, 1f, elapsedTime / testFadeDuration);
                CameraManager.SetStoryboardCameraAlpha(alpha);
                yield return null;
            }
            
            // Ensure final alpha is 1 (fully black)
            CameraManager.SetStoryboardCameraAlpha(1f);
            Debug.Log("Fade Out Test Complete");
            
            // Wait a moment then clear
            yield return new WaitForSecondsRealtime(1f);
            CameraManager.SetStoryboardCameraAlpha(0f);
        }
        
        private void OnGUI()
        {
            GUILayout.BeginArea(new Rect(10, 10, 300, 200));
            GUILayout.Label("Timeline Fade Test Controls:");
            GUILayout.Label($"Press {testFadeInKey} to test Fade In");
            GUILayout.Label($"Press {testFadeOutKey} to test Fade Out");
            GUILayout.Label($"Press {testFullTimelineKey} to test Full Timeline");
            
            if (timelineTrigger != null)
            {
                GUILayout.Space(10);
                GUILayout.Label("Timeline Settings:");
                GUILayout.Label($"Fade Enabled: {timelineTrigger.enableFade}");
                GUILayout.Label($"Fade In Duration: {timelineTrigger.fadeInDuration}s");
                GUILayout.Label($"Fade Out Duration: {timelineTrigger.fadeOutDuration}s");
                GUILayout.Label($"Fade In Delay: {timelineTrigger.fadeInDelay}s");
                GUILayout.Label($"Fade Out Delay: {timelineTrigger.fadeOutDelay}s");
            }
            GUILayout.EndArea();
        }
    }
}
